/**
 * 简单的截图脚本 - 使用 npx playwright 直接运行
 */

import fs from "fs";
import { chromium } from "playwright";

async function takeScreenshot() {
  console.log("🚀 启动浏览器...");

  const browser = await chromium.launch({
    headless: false,
    slowMo: 500,
  });

  const page = await browser.newPage();

  try {
    console.log("📱 访问登录页面...");
    await page.goto("http://localhost:5173/auth/login", {
      waitUntil: "networkidle",
      timeout: 30000,
    });

    // 等待页面完全加载
    await page.waitForTimeout(3000);

    console.log("📸 截取页面截图...");

    // 创建截图目录
    if (!fs.existsSync("screenshots")) {
      fs.mkdirSync("screenshots");
    }

    // 截取全页面截图
    await page.screenshot({
      path: "screenshots/login-page-full.png",
      fullPage: true,
    });

    // 截取视窗截图
    await page.screenshot({
      path: "screenshots/login-page-viewport.png",
      fullPage: false,
    });

    console.log("🔍 检查页面元素...");

    // 检查主要元素
    const title = await page.title();
    console.log(`页面标题: ${title}`);

    // 检查 Google 按钮
    const googleButton = page.locator('button:has-text("Continue with Google")');
    const googleButtonExists = (await googleButton.count()) > 0;
    console.log(`Google 按钮存在: ${googleButtonExists}`);

    if (googleButtonExists) {
      // 高亮 Google 按钮并截图
      await googleButton.first().highlight();
      await page.screenshot({
        path: "screenshots/google-button-highlighted.png",
        fullPage: false,
      });
    }

    // 检查页面文本内容
    const pageContent = await page.textContent("body");
    const hasSignInText = pageContent.includes("Sign In");
    const hasGoogleText = pageContent.includes("Google");

    console.log(`包含 "Sign In" 文本: ${hasSignInText}`);
    console.log(`包含 "Google" 文本: ${hasGoogleText}`);

    // 等待一段时间观察
    console.log("⏳ 等待 5 秒观察页面...");
    await page.waitForTimeout(5000);

    console.log("✅ 截图完成！");
    console.log("📁 截图保存在 screenshots/ 目录");
  } catch (error) {
    console.error("❌ 错误:", error);
  } finally {
    await browser.close();
  }
}

// 运行截图
takeScreenshot().catch(console.error);
