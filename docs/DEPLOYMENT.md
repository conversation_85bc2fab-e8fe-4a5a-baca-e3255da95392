# Deployment Guide

This guide covers deploying the Remix + Vercel + Neon starter to production.

## Prerequisites

- [Vercel Account](https://vercel.com)
- [Neon Database Account](https://neon.tech)
- [GitHub Repository](https://github.com) (for CI/CD)
- Domain name (optional)

## Quick Start

1. **Fork/Clone Repository**
2. **Set up Neon Database**
3. **Deploy to Vercel**
4. **Configure Environment Variables**
5. **Run Database Migrations**
6. **Verify Deployment**

## Detailed Setup

### 1. Neon Database Setup

#### Create Database

1. Go to [Neon Console](https://console.neon.tech)
2. Create new project
3. Choose region (preferably same as Vercel deployment)
4. Note down connection string

#### Configure Database

```sql
-- Create database (if not exists)
CREATE DATABASE your_app_name;

-- Create necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
```

#### Security Settings

1. Enable connection pooling
2. Set up read replicas (for production)
3. Configure backup retention
4. Set up monitoring alerts

### 2. Vercel Deployment

#### Connect Repository

1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your GitHub repository
4. Configure build settings:
   - Framework: Remix
   - Build Command: `pnpm run build`
   - Output Directory: `build`
   - Install Command: `pnpm install`

#### Environment Variables

Set these in Vercel Dashboard → Project → Settings → Environment Variables:

```bash
# Database
DATABASE_URL="******************************"

# Authentication
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
SESSION_SECRET="your-session-secret"

# AI Providers
OPENAI_API_KEY="your-openai-key"
DEEPSEEK_API_KEY="your-deepseek-key"
OPENROUTER_API_KEY="your-openrouter-key"
SILICONFLOW_API_KEY="your-siliconflow-key"
REPLICATE_API_TOKEN="your-replicate-token"

# Payment
STRIPE_SECRET_KEY="your-stripe-secret"
STRIPE_PUBLISHABLE_KEY="your-stripe-public"
STRIPE_WEBHOOK_SECRET="your-webhook-secret"

# Email
RESEND_API_KEY="your-resend-key"

# Storage
VERCEL_BLOB_READ_WRITE_TOKEN="your-blob-token"

# Analytics (Optional)
GOOGLE_ANALYTICS_ID="your-ga-id"

# Application
APP_URL="https://your-domain.com"
NODE_ENV="production"
```

#### Domain Configuration

1. Add custom domain in Vercel Dashboard
2. Configure DNS records
3. Enable HTTPS (automatic with Vercel)
4. Set up redirects if needed

### 3. Database Migrations

#### Run Initial Migration

```bash
# Install dependencies locally
pnpm install

# Set DATABASE_URL in local .env
echo "DATABASE_URL=your_neon_connection_string" > .env

# Run migrations
pnpm run db:migrate:add-fields:dry  # Dry run first
pnpm run db:migrate:add-fields      # Apply changes
```

#### Verify Schema

```sql
-- Connect to your Neon database
\d+ users
\d+ accounts
\d+ orders
\d+ subscriptions
-- Verify all tables exist with correct columns
```

### 4. Post-Deployment Setup

#### Configure OAuth

1. **Google OAuth**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Create OAuth 2.0 credentials
   - Add authorized redirect URIs:
     - `https://your-domain.com/auth/google/callback`
   - Update environment variables

2. **Stripe Webhooks**
   - Go to [Stripe Dashboard](https://dashboard.stripe.com)
   - Create webhook endpoint: `https://your-domain.com/api/stripe-notify`
   - Select events to listen for
   - Copy webhook secret to environment variables

#### Test Critical Paths

1. User registration/login
2. Payment processing
3. AI features
4. Email notifications
5. Database operations

## Environment-Specific Configurations

### Development

```bash
# .env.local
DATABASE_URL="postgresql://localhost:5432/dev_db"
NODE_ENV="development"
APP_URL="http://localhost:3000"
ENABLE_QUERY_LOGGING="true"
```

### Staging

```bash
# Vercel Environment Variables (Preview)
DATABASE_URL="postgresql://staging_connection"
NODE_ENV="staging"
APP_URL="https://staging-your-app.vercel.app"
ENABLE_QUERY_LOGGING="true"
```

### Production

```bash
# Vercel Environment Variables (Production)
DATABASE_URL="postgresql://production_connection"
NODE_ENV="production"
APP_URL="https://your-domain.com"
ENABLE_QUERY_LOGGING="false"
```

## Monitoring Setup

### Vercel Analytics

1. Enable Vercel Analytics in dashboard
2. Add analytics code to app
3. Monitor performance metrics

### Database Monitoring

1. Enable Neon monitoring
2. Set up alerts for:
   - High CPU usage
   - Connection limits
   - Query performance
   - Storage usage

### Application Monitoring

1. **Error Tracking**
   - Set up Sentry or similar
   - Configure error boundaries
   - Monitor API endpoints

2. **Performance Monitoring**
   - Use Vercel Speed Insights
   - Monitor Core Web Vitals
   - Track API response times

3. **Uptime Monitoring**
   - Set up external monitoring
   - Configure health check endpoints
   - Monitor critical user flows

## Security Checklist

- [ ] HTTPS enabled
- [ ] Environment variables secured
- [ ] Database access restricted
- [ ] API rate limiting enabled
- [ ] Input validation implemented
- [ ] CSRF protection enabled
- [ ] Security headers configured
- [ ] Dependency vulnerabilities checked

## Backup Strategy

### Database Backups

1. **Automated Backups**
   - Neon provides automatic backups
   - Configure retention period
   - Test restore procedures

2. **Manual Backups**
   ```bash
   # Create backup
   pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql
   
   # Restore backup
   psql $DATABASE_URL < backup_20240123.sql
   ```

### Code Backups

1. Git repository (primary)
2. Vercel deployment history
3. Local development backups

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check build logs in Vercel
   - Verify dependencies
   - Check TypeScript errors

2. **Database Connection Issues**
   - Verify connection string
   - Check Neon database status
   - Review connection limits

3. **Environment Variable Issues**
   - Verify all required variables are set
   - Check variable names (case sensitive)
   - Redeploy after changes

### Debug Tools

1. **Vercel Logs**
   ```bash
   vercel logs your-deployment-url
   ```

2. **Database Queries**
   ```bash
   # Connect to Neon database
   psql $DATABASE_URL
   ```

3. **Local Development**
   ```bash
   # Run locally with production env
   vercel dev
   ```

## Performance Optimization

### Vercel Optimizations

1. Enable Edge Functions where appropriate
2. Configure caching headers
3. Optimize bundle size
4. Use Vercel Image Optimization

### Database Optimizations

1. Add appropriate indexes
2. Use connection pooling
3. Optimize query patterns
4. Monitor slow queries

### Application Optimizations

1. Implement caching strategies
2. Optimize React components
3. Use code splitting
4. Minimize bundle size

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review error logs
   - Check performance metrics
   - Monitor resource usage

2. **Monthly**
   - Update dependencies
   - Review security alerts
   - Backup verification

3. **Quarterly**
   - Security audit
   - Performance review
   - Cost optimization

### Update Process

1. Test changes in development
2. Deploy to staging
3. Run automated tests
4. Deploy to production
5. Monitor for issues
6. Rollback if necessary

## Support

For deployment issues:

1. Check Vercel documentation
2. Review Neon documentation
3. Check application logs
4. Contact support if needed

## Additional Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Neon Documentation](https://neon.tech/docs)
- [Remix Documentation](https://remix.run/docs)
- [Project Repository](https://github.com/your-username/your-repo)
