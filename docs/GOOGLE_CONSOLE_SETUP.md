# Google Console 配置指南

## 当前项目信息
- **Client ID**: `982969788186-76f0ealh48g2pnhomp01ftescge62hgg.apps.googleusercontent.com`
- **开发服务器**: `http://localhost:5173`
- **备用端口**: `http://localhost:3000`

## 快速配置步骤

### 1. 打开 Google Console
访问: https://console.developers.google.com/apis/credentials

### 2. 找到你的 OAuth 2.0 客户端 ID
查找 Client ID: `982969788186-76f0ealh48g2pnhomp01ftescge62hgg.apps.googleusercontent.com`

### 3. 编辑客户端 ID 配置

#### 授权的 JavaScript 来源 (必须添加)
```
http://localhost:5173
http://localhost:3000
http://127.0.0.1:5173
http://127.0.0.1:3000
```

#### 授权的重定向 URI (必须添加)
```
http://localhost:5173/auth/google/callback
http://localhost:5173/auth/google/oauth
http://localhost:3000/auth/google/callback
http://localhost:3000/auth/google/oauth
```

### 4. 保存配置
点击"保存"按钮，配置可能需要几分钟到几小时才能生效。

## 常见问题排查

### 问题 1: "unregistered_origin" 错误
**原因**: 当前域名未在 Google Console 中注册
**解决方案**: 
1. 确保在"授权的 JavaScript 来源"中添加了 `http://localhost:5173`
2. 等待配置生效（最多 24 小时）
3. 尝试使用无痕模式

### 问题 2: "opt_out_or_no_session" 错误
**原因**: 用户之前选择了不显示 One Tap，或者没有 Google 会话
**解决方案**:
1. 清除浏览器所有数据
2. 使用无痕模式
3. 确保已登录 Google 账户

### 问题 3: "invalid_client" 错误
**原因**: Client ID 配置错误
**解决方案**:
1. 检查 `.env` 文件中的 `GOOGLE_CLIENT_ID`
2. 确保 Client ID 以 `.apps.googleusercontent.com` 结尾
3. 重新生成 Client ID

### 问题 4: One Tap 不显示
**可能原因**:
1. 配置未生效（等待时间不够）
2. 浏览器阻止了弹窗
3. 用户之前拒绝过
4. 网络连接问题

**解决方案**:
1. 等待更长时间（最多 24 小时）
2. 检查浏览器弹窗设置
3. 清除浏览器数据
4. 检查网络连接

## 测试步骤

### 1. 基本测试
访问: http://localhost:5173/test/simple-google

### 2. 详细调试
访问: http://localhost:5173/test/google-auth

### 3. 配置检查
访问: http://localhost:5173/debug/google-config

### 4. 实际登录测试
访问: http://localhost:5173/auth/login

## 备用方案

如果 Google One Tap 仍然无法工作，可以使用传统的 OAuth 流程：

1. 访问: http://localhost:5173/auth/google/oauth
2. 这将重定向到 Google 授权页面
3. 授权后会重定向回应用

## 配置验证清单

- [ ] Google Console 中已添加 `http://localhost:5173` 到授权的 JavaScript 来源
- [ ] Google Console 中已添加 `http://localhost:5173/auth/google/callback` 到重定向 URI
- [ ] `.env` 文件中的 `GOOGLE_CLIENT_ID` 正确
- [ ] `.env` 文件中的 `GOOGLE_CLIENT_SECRET` 正确
- [ ] 已等待配置生效（至少 10 分钟）
- [ ] 已清除浏览器缓存和数据
- [ ] 已在 Google 中登录账户

## 调试命令

### 检查环境变量
```bash
echo $GOOGLE_CLIENT_ID
echo $GOOGLE_CLIENT_SECRET
```

### 测试网络连接
```bash
curl -I https://accounts.google.com/gsi/client
```

### 验证 Client ID
```bash
curl "https://oauth2.googleapis.com/tokeninfo?client_id=YOUR_CLIENT_ID"
```

## 联系支持

如果以上步骤都无法解决问题，请：

1. 检查 Google Console 的配额和限制
2. 确认项目状态正常
3. 联系 Google Cloud 支持

## 更新日志

- 2025-06-23: 创建配置指南
- 配置的 Client ID 创建时间: 2025-06-22 13:26:35 GMT+8
