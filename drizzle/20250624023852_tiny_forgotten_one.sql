CREATE TYPE "public"."feedback_priority" AS ENUM('low', 'medium', 'high', 'urgent');--> statement-breakpoint
CREATE TYPE "public"."feedback_status" AS ENUM('open', 'in_progress', 'resolved', 'closed', 'duplicate');--> statement-breakpoint
CREATE TYPE "public"."feedback_type" AS ENUM('bug_report', 'feature_request', 'improvement', 'question', 'complaint', 'compliment', 'other');--> statement-breakpoint
ALTER TYPE "public"."notification_type" ADD VALUE 'payment';--> statement-breakpoint
ALTER TYPE "public"."notification_type" ADD VALUE 'credit';--> statement-breakpoint
ALTER TYPE "public"."notification_type" ADD VALUE 'usage';--> statement-breakpoint
ALTER TYPE "public"."notification_type" ADD VALUE 'system';--> statement-breakpoint
ALTER TYPE "public"."notification_type" ADD VALUE 'security';--> statement-breakpoint
CREATE TABLE "conversations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"title" varchar(255) NOT NULL,
	"model" varchar(100),
	"provider" varchar(50),
	"is_archived" boolean DEFAULT false NOT NULL,
	"last_message_at" timestamp,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "generated_images" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"message_id" uuid,
	"prompt" text NOT NULL,
	"model" varchar(100) NOT NULL,
	"provider" varchar(50) NOT NULL,
	"image_url" text NOT NULL,
	"width" integer,
	"height" integer,
	"parameters" jsonb,
	"cost" integer,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "messages" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"conversation_id" uuid NOT NULL,
	"role" varchar(20) NOT NULL,
	"content" text NOT NULL,
	"model" varchar(100),
	"provider" varchar(50),
	"token_count" integer,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "api_usage" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" uuid,
	"api_key_id" uuid,
	"endpoint" varchar(255) NOT NULL,
	"method" varchar(10) NOT NULL,
	"status_code" integer NOT NULL,
	"response_time" integer,
	"request_size" integer,
	"response_size" integer,
	"user_agent" text,
	"ip_address" varchar(45),
	"referer" text,
	"model" varchar(100),
	"provider" varchar(50),
	"token_count" integer,
	"cost" numeric(10, 6),
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "rate_limits" (
	"id" serial PRIMARY KEY NOT NULL,
	"identifier" varchar(255) NOT NULL,
	"identifier_type" varchar(50) NOT NULL,
	"endpoint" varchar(255) NOT NULL,
	"window_start" timestamp NOT NULL,
	"window_end" timestamp NOT NULL,
	"request_count" integer DEFAULT 0 NOT NULL,
	"limit_count" integer NOT NULL,
	"reset_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "system_metrics" (
	"id" serial PRIMARY KEY NOT NULL,
	"timestamp" timestamp DEFAULT now() NOT NULL,
	"metric_name" varchar(100) NOT NULL,
	"metric_value" numeric(15, 6) NOT NULL,
	"unit" varchar(20),
	"tags" jsonb,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "usage_stats" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" uuid,
	"date" varchar(10) NOT NULL,
	"period" varchar(10) NOT NULL,
	"total_requests" integer DEFAULT 0 NOT NULL,
	"successful_requests" integer DEFAULT 0 NOT NULL,
	"error_requests" integer DEFAULT 0 NOT NULL,
	"avg_response_time" integer,
	"total_tokens" integer DEFAULT 0 NOT NULL,
	"total_cost" numeric(10, 6) DEFAULT '0.000000' NOT NULL,
	"unique_models" integer DEFAULT 0 NOT NULL,
	"unique_providers" integer DEFAULT 0 NOT NULL,
	"total_request_size" integer DEFAULT 0 NOT NULL,
	"total_response_size" integer DEFAULT 0 NOT NULL,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sessions" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"session_token" varchar(255) NOT NULL,
	"refresh_token" varchar(255) NOT NULL,
	"expires_at" timestamp NOT NULL,
	"refresh_expires_at" timestamp NOT NULL,
	"user_agent" text,
	"ip_address" varchar(45),
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "sessions_session_token_unique" UNIQUE("session_token"),
	CONSTRAINT "sessions_refresh_token_unique" UNIQUE("refresh_token")
);
--> statement-breakpoint
ALTER TABLE "accounts" DROP CONSTRAINT "accounts_slug_unique";--> statement-breakpoint
ALTER TABLE "api_keys" DROP CONSTRAINT "api_keys_api_key_unique";--> statement-breakpoint
ALTER TABLE "billing_customers" DROP CONSTRAINT "billing_customers_customer_id_provider_unique";--> statement-breakpoint
ALTER TABLE "invitations" DROP CONSTRAINT "invitations_invite_token_unique";--> statement-breakpoint
ALTER TABLE "role_permissions" DROP CONSTRAINT "role_permission_unique";--> statement-breakpoint
ALTER TABLE "accounts_memberships" DROP CONSTRAINT "accounts_memberships_account_role_roles_name_fk";
--> statement-breakpoint
ALTER TABLE "accounts_memberships" DROP CONSTRAINT "accounts_memberships_created_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "accounts_memberships" DROP CONSTRAINT "accounts_memberships_updated_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "api_keys" DROP CONSTRAINT "api_keys_account_id_accounts_id_fk";
--> statement-breakpoint
ALTER TABLE "billing_customers" DROP CONSTRAINT "billing_customers_account_id_accounts_id_fk";
--> statement-breakpoint
ALTER TABLE "credit_transactions" DROP CONSTRAINT "credit_transactions_account_id_accounts_id_fk";
--> statement-breakpoint
ALTER TABLE "invitations" DROP CONSTRAINT "invitations_account_id_accounts_id_fk";
--> statement-breakpoint
ALTER TABLE "invitations" DROP CONSTRAINT "invitations_role_roles_name_fk";
--> statement-breakpoint
ALTER TABLE "notifications" DROP CONSTRAINT "notifications_account_id_accounts_id_fk";
--> statement-breakpoint
ALTER TABLE "orders" DROP CONSTRAINT "orders_account_id_accounts_id_fk";
--> statement-breakpoint
ALTER TABLE "orders" DROP CONSTRAINT "orders_billing_customer_id_billing_customers_id_fk";
--> statement-breakpoint
ALTER TABLE "role_permissions" DROP CONSTRAINT "role_permissions_role_roles_name_fk";
--> statement-breakpoint
ALTER TABLE "subscriptions" DROP CONSTRAINT "subscriptions_account_id_accounts_id_fk";
--> statement-breakpoint
ALTER TABLE "subscriptions" DROP CONSTRAINT "subscriptions_billing_customer_id_billing_customers_id_fk";
--> statement-breakpoint
ALTER TABLE "role_permissions" ALTER COLUMN "permission" SET DATA TYPE text;--> statement-breakpoint
DROP TYPE "public"."app_permissions";--> statement-breakpoint
CREATE TYPE "public"."app_permissions" AS ENUM('roles.manage', 'billing.manage', 'notifications.manage', 'users.manage', 'orders.manage', 'feedback.manage', 'analytics.view', 'api_keys.manage', 'content.manage', 'system.manage');--> statement-breakpoint
ALTER TABLE "role_permissions" ALTER COLUMN "permission" SET DATA TYPE "public"."app_permissions" USING "permission"::"public"."app_permissions";--> statement-breakpoint
DROP INDEX "accounts_memberships_account_id_idx";--> statement-breakpoint
DROP INDEX "accounts_memberships_user_id_idx";--> statement-breakpoint
DROP INDEX "affiliates_user_uuid_idx";--> statement-breakpoint
DROP INDEX "affiliates_invited_by_idx";--> statement-breakpoint
DROP INDEX "affiliates_status_idx";--> statement-breakpoint
DROP INDEX "api_keys_api_key_idx";--> statement-breakpoint
DROP INDEX "api_keys_user_uuid_idx";--> statement-breakpoint
DROP INDEX "api_keys_account_id_idx";--> statement-breakpoint
DROP INDEX "api_keys_status_idx";--> statement-breakpoint
DROP INDEX "billing_customers_account_id_idx";--> statement-breakpoint
DROP INDEX "credit_transactions_user_uuid_idx";--> statement-breakpoint
DROP INDEX "credit_transactions_account_id_idx";--> statement-breakpoint
DROP INDEX "credit_transactions_trans_type_idx";--> statement-breakpoint
DROP INDEX "credit_transactions_created_at_idx";--> statement-breakpoint
DROP INDEX "credit_transactions_expires_at_idx";--> statement-breakpoint
DROP INDEX "invitations_account_id_idx";--> statement-breakpoint
DROP INDEX "invitations_invite_token_idx";--> statement-breakpoint
DROP INDEX "invitations_expires_at_idx";--> statement-breakpoint
DROP INDEX "notifications_account_id_idx";--> statement-breakpoint
DROP INDEX "notifications_user_id_idx";--> statement-breakpoint
DROP INDEX "notifications_created_at_idx";--> statement-breakpoint
DROP INDEX "order_items_order_id_idx";--> statement-breakpoint
DROP INDEX "order_items_product_id_idx";--> statement-breakpoint
DROP INDEX "orders_account_id_idx";--> statement-breakpoint
DROP INDEX "orders_created_at_idx";--> statement-breakpoint
DROP INDEX "posts_author_id_idx";--> statement-breakpoint
DROP INDEX "posts_created_at_idx";--> statement-breakpoint
DROP INDEX "role_permissions_role_idx";--> statement-breakpoint
DROP INDEX "subscription_items_subscription_id_idx";--> statement-breakpoint
DROP INDEX "subscription_items_product_id_idx";--> statement-breakpoint
DROP INDEX "subscriptions_account_id_idx";--> statement-breakpoint
DROP INDEX "subscriptions_period_ends_at_idx";--> statement-breakpoint
ALTER TABLE "accounts_memberships" DROP CONSTRAINT "accounts_memberships_account_id_user_id_pk";--> statement-breakpoint
ALTER TABLE "accounts" ALTER COLUMN "id" SET DATA TYPE serial;--> statement-breakpoint
ALTER TABLE "accounts_memberships" ALTER COLUMN "account_id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "accounts_memberships" ALTER COLUMN "user_id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "affiliates" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "affiliates" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "api_keys" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "api_keys" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "billing_customers" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "billing_customers" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "billing_customers" ALTER COLUMN "customer_id" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "billing_customers" ALTER COLUMN "email" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "billing_customers" ALTER COLUMN "email" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "credit_transactions" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "credit_transactions" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "credit_transactions" ALTER COLUMN "trans_no" SET DATA TYPE varchar(50);--> statement-breakpoint
ALTER TABLE "feedback" ALTER COLUMN "status" SET DEFAULT 'open'::"public"."feedback_status";--> statement-breakpoint
ALTER TABLE "feedback" ALTER COLUMN "status" SET DATA TYPE "public"."feedback_status" USING "status"::"public"."feedback_status";--> statement-breakpoint
ALTER TABLE "feedback" ALTER COLUMN "updated_at" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "invitations" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "invitations" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "invitations" ALTER COLUMN "email" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "invitations" ALTER COLUMN "invited_by" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "notifications" ALTER COLUMN "user_id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "notifications" ALTER COLUMN "user_id" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "notifications" ALTER COLUMN "title" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "notifications" ALTER COLUMN "type" SET DEFAULT 'system';--> statement-breakpoint
ALTER TABLE "notifications" ALTER COLUMN "link" SET DATA TYPE varchar(500);--> statement-breakpoint
ALTER TABLE "order_items" ALTER COLUMN "id" SET DATA TYPE serial;--> statement-breakpoint
ALTER TABLE "order_items" ALTER COLUMN "order_id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "order_items" ALTER COLUMN "product_id" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "orders" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "orders" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "orders" ALTER COLUMN "order_no" SET DATA TYPE varchar(100);--> statement-breakpoint
ALTER TABLE "orders" ALTER COLUMN "user_uuid" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "orders" ALTER COLUMN "user_email" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "orders" ALTER COLUMN "currency" SET DATA TYPE varchar(3);--> statement-breakpoint
ALTER TABLE "orders" ALTER COLUMN "currency" SET DEFAULT 'USD';--> statement-breakpoint
ALTER TABLE "orders" ALTER COLUMN "provider_order_id" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "posts" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "posts" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "posts" ALTER COLUMN "title" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "posts" ALTER COLUMN "author_id" SET DATA TYPE uuid;--> statement-breakpoint
/* 
    Unfortunately in current drizzle-kit version we can't automatically get name for primary key.
    We are working on making it available!

    Meanwhile you can:
        1. Check pk name in your database, by running
            SELECT constraint_name FROM information_schema.table_constraints
            WHERE table_schema = 'public'
                AND table_name = 'roles'
                AND constraint_type = 'PRIMARY KEY';
        2. Uncomment code below and paste pk name manually
        
    Hope to release this update as soon as possible
*/

-- ALTER TABLE "roles" DROP CONSTRAINT "<constraint_name>";--> statement-breakpoint
ALTER TABLE "roles" ALTER COLUMN "name" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "subscription_items" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "subscription_items" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "subscription_items" ALTER COLUMN "subscription_id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "subscription_items" ALTER COLUMN "product_id" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "subscriptions" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "subscriptions" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "subscriptions" ALTER COLUMN "active" SET DEFAULT true;--> statement-breakpoint
ALTER TABLE "subscriptions" ALTER COLUMN "currency" SET DATA TYPE varchar(3);--> statement-breakpoint
ALTER TABLE "subscriptions" ALTER COLUMN "currency" SET DEFAULT 'USD';--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "uuid" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "uuid" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "uuid" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "name" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "name" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "email" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "invite_code" SET DATA TYPE varchar(10);--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "invited_by" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "signin_type" SET DATA TYPE varchar(50);--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "signin_provider" SET DATA TYPE varchar(50);--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "signin_openid" SET DATA TYPE varchar(255);--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "signin_ip" SET DATA TYPE varchar(45);--> statement-breakpoint
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_permission_pk" PRIMARY KEY("role_id","permission");--> statement-breakpoint
ALTER TABLE "accounts" ADD COLUMN "user_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "accounts" ADD COLUMN "provider" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "accounts" ADD COLUMN "provider_user_id" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "accounts" ADD COLUMN "access_token" text;--> statement-breakpoint
ALTER TABLE "accounts" ADD COLUMN "refresh_token" text;--> statement-breakpoint
ALTER TABLE "accounts" ADD COLUMN "expires_at" timestamp;--> statement-breakpoint
ALTER TABLE "accounts_memberships" ADD COLUMN "id" serial PRIMARY KEY NOT NULL;--> statement-breakpoint
ALTER TABLE "accounts_memberships" ADD COLUMN "role_id" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "accounts_memberships" ADD COLUMN "invited_by" uuid;--> statement-breakpoint
ALTER TABLE "accounts_memberships" ADD COLUMN "invited_at" timestamp;--> statement-breakpoint
ALTER TABLE "accounts_memberships" ADD COLUMN "joined_at" timestamp;--> statement-breakpoint
ALTER TABLE "affiliates" ADD COLUMN "user_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "affiliates" ADD COLUMN "code" varchar(50) NOT NULL;--> statement-breakpoint
ALTER TABLE "affiliates" ADD COLUMN "commission_rate" numeric(5, 4) DEFAULT '0.1000' NOT NULL;--> statement-breakpoint
ALTER TABLE "affiliates" ADD COLUMN "total_earnings" numeric(10, 2) DEFAULT '0.00' NOT NULL;--> statement-breakpoint
ALTER TABLE "affiliates" ADD COLUMN "is_active" boolean DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE "affiliates" ADD COLUMN "updated_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "api_keys" ADD COLUMN "user_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "api_keys" ADD COLUMN "name" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "api_keys" ADD COLUMN "key_hash" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "api_keys" ADD COLUMN "is_active" boolean DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE "api_keys" ADD COLUMN "permissions" text;--> statement-breakpoint
ALTER TABLE "api_keys" ADD COLUMN "updated_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "billing_customers" ADD COLUMN "user_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "billing_customers" ADD COLUMN "name" varchar(255);--> statement-breakpoint
ALTER TABLE "billing_customers" ADD COLUMN "created_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "billing_customers" ADD COLUMN "updated_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "credit_transactions" ADD COLUMN "user_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "credit_transactions" ADD COLUMN "amount" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "credit_transactions" ADD COLUMN "type" varchar(50) NOT NULL;--> statement-breakpoint
ALTER TABLE "credit_transactions" ADD COLUMN "order_id" uuid;--> statement-breakpoint
ALTER TABLE "credit_transactions" ADD COLUMN "metadata" jsonb;--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "user_id" uuid;--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "type" "feedback_type" NOT NULL;--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "priority" "feedback_priority" DEFAULT 'medium' NOT NULL;--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "title" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "description" text NOT NULL;--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "email" varchar(255);--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "user_agent" text;--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "url" varchar(500);--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "attachments" jsonb;--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "admin_notes" text;--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "resolved_at" timestamp;--> statement-breakpoint
ALTER TABLE "feedback" ADD COLUMN "resolved_by" uuid;--> statement-breakpoint
ALTER TABLE "invitations" ADD COLUMN "role_id" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "invitations" ADD COLUMN "token" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "invitations" ADD COLUMN "accepted_by" uuid;--> statement-breakpoint
ALTER TABLE "notifications" ADD COLUMN "updated_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "order_items" ADD COLUMN "price_id" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "order_items" ADD COLUMN "unit_price" numeric(10, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "order_items" ADD COLUMN "total_price" numeric(10, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "order_items" ADD COLUMN "metadata" jsonb;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "user_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "affiliate_id" uuid;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "order_number" varchar(100) NOT NULL;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "subtotal" numeric(10, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "tax" numeric(10, 2) DEFAULT '0.00' NOT NULL;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "total" numeric(10, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "provider" "billing_provider" NOT NULL;--> statement-breakpoint
ALTER TABLE "orders" ADD COLUMN "metadata" jsonb;--> statement-breakpoint
ALTER TABLE "posts" ADD COLUMN "slug" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "posts" ADD COLUMN "excerpt" text;--> statement-breakpoint
ALTER TABLE "posts" ADD COLUMN "published_at" timestamp;--> statement-breakpoint
ALTER TABLE "posts" ADD COLUMN "tags" text;--> statement-breakpoint
ALTER TABLE "posts" ADD COLUMN "metadata" jsonb;--> statement-breakpoint
ALTER TABLE "role_permissions" ADD COLUMN "role_id" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "roles" ADD COLUMN "id" serial PRIMARY KEY NOT NULL;--> statement-breakpoint
ALTER TABLE "roles" ADD COLUMN "description" text;--> statement-breakpoint
ALTER TABLE "subscription_items" ADD COLUMN "provider" "billing_provider" NOT NULL;--> statement-breakpoint
ALTER TABLE "subscription_items" ADD COLUMN "subscription_item_id" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "subscription_items" ADD COLUMN "price_id" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "subscription_items" ADD COLUMN "metadata" jsonb;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "user_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "provider" "billing_provider" NOT NULL;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "subscription_id" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "customer_id" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "current_period_start" timestamp NOT NULL;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "current_period_end" timestamp NOT NULL;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "canceled_at" timestamp;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "trial_start" timestamp;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "trial_end" timestamp;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "metadata" jsonb;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "email_verified" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "onboarding_completed" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "onboarding_step" varchar(50);--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "preferences" text;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "theme" varchar(20) DEFAULT 'system';--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "google_sub" varchar(255);--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "password_hash" text;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "last_login_at" timestamp;--> statement-breakpoint
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "generated_images" ADD CONSTRAINT "generated_images_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "generated_images" ADD CONSTRAINT "generated_images_message_id_messages_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."messages"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_conversation_id_conversations_id_fk" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "api_usage" ADD CONSTRAINT "api_usage_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "usage_stats" ADD CONSTRAINT "usage_stats_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "conversations_user_idx" ON "conversations" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "conversations_archived_idx" ON "conversations" USING btree ("is_archived");--> statement-breakpoint
CREATE INDEX "conversations_last_message_idx" ON "conversations" USING btree ("last_message_at");--> statement-breakpoint
CREATE INDEX "conversations_model_idx" ON "conversations" USING btree ("model");--> statement-breakpoint
CREATE INDEX "conversations_provider_idx" ON "conversations" USING btree ("provider");--> statement-breakpoint
CREATE INDEX "generated_images_user_idx" ON "generated_images" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "generated_images_message_idx" ON "generated_images" USING btree ("message_id");--> statement-breakpoint
CREATE INDEX "generated_images_model_idx" ON "generated_images" USING btree ("model");--> statement-breakpoint
CREATE INDEX "generated_images_provider_idx" ON "generated_images" USING btree ("provider");--> statement-breakpoint
CREATE INDEX "generated_images_created_idx" ON "generated_images" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "messages_conversation_idx" ON "messages" USING btree ("conversation_id");--> statement-breakpoint
CREATE INDEX "messages_role_idx" ON "messages" USING btree ("role");--> statement-breakpoint
CREATE INDEX "messages_created_idx" ON "messages" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "messages_model_idx" ON "messages" USING btree ("model");--> statement-breakpoint
CREATE INDEX "messages_provider_idx" ON "messages" USING btree ("provider");--> statement-breakpoint
CREATE INDEX "api_usage_user_idx" ON "api_usage" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "api_usage_api_key_idx" ON "api_usage" USING btree ("api_key_id");--> statement-breakpoint
CREATE INDEX "api_usage_endpoint_idx" ON "api_usage" USING btree ("endpoint");--> statement-breakpoint
CREATE INDEX "api_usage_status_idx" ON "api_usage" USING btree ("status_code");--> statement-breakpoint
CREATE INDEX "api_usage_created_idx" ON "api_usage" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "api_usage_model_idx" ON "api_usage" USING btree ("model");--> statement-breakpoint
CREATE INDEX "api_usage_provider_idx" ON "api_usage" USING btree ("provider");--> statement-breakpoint
CREATE INDEX "api_usage_user_endpoint_idx" ON "api_usage" USING btree ("user_id","endpoint");--> statement-breakpoint
CREATE INDEX "api_usage_user_created_idx" ON "api_usage" USING btree ("user_id","created_at");--> statement-breakpoint
CREATE INDEX "rate_limits_identifier_idx" ON "rate_limits" USING btree ("identifier");--> statement-breakpoint
CREATE INDEX "rate_limits_endpoint_idx" ON "rate_limits" USING btree ("endpoint");--> statement-breakpoint
CREATE INDEX "rate_limits_reset_idx" ON "rate_limits" USING btree ("reset_at");--> statement-breakpoint
CREATE INDEX "rate_limits_window_idx" ON "rate_limits" USING btree ("window_start","window_end");--> statement-breakpoint
CREATE INDEX "rate_limits_identifier_endpoint_idx" ON "rate_limits" USING btree ("identifier","endpoint");--> statement-breakpoint
CREATE INDEX "system_metrics_timestamp_idx" ON "system_metrics" USING btree ("timestamp");--> statement-breakpoint
CREATE INDEX "system_metrics_name_idx" ON "system_metrics" USING btree ("metric_name");--> statement-breakpoint
CREATE INDEX "system_metrics_name_timestamp_idx" ON "system_metrics" USING btree ("metric_name","timestamp");--> statement-breakpoint
CREATE INDEX "usage_stats_user_idx" ON "usage_stats" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "usage_stats_date_idx" ON "usage_stats" USING btree ("date");--> statement-breakpoint
CREATE INDEX "usage_stats_period_idx" ON "usage_stats" USING btree ("period");--> statement-breakpoint
CREATE INDEX "usage_stats_user_date_idx" ON "usage_stats" USING btree ("user_id","date");--> statement-breakpoint
CREATE INDEX "usage_stats_user_period_idx" ON "usage_stats" USING btree ("user_id","period");--> statement-breakpoint
CREATE INDEX "sessions_user_idx" ON "sessions" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "sessions_token_idx" ON "sessions" USING btree ("session_token");--> statement-breakpoint
CREATE INDEX "sessions_refresh_token_idx" ON "sessions" USING btree ("refresh_token");--> statement-breakpoint
CREATE INDEX "sessions_expires_idx" ON "sessions" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "sessions_active_idx" ON "sessions" USING btree ("is_active");--> statement-breakpoint
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "accounts_memberships" ADD CONSTRAINT "accounts_memberships_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "accounts_memberships" ADD CONSTRAINT "accounts_memberships_invited_by_users_id_fk" FOREIGN KEY ("invited_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "affiliates" ADD CONSTRAINT "affiliates_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "api_keys" ADD CONSTRAINT "api_keys_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "billing_customers" ADD CONSTRAINT "billing_customers_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "credit_transactions" ADD CONSTRAINT "credit_transactions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "credit_transactions" ADD CONSTRAINT "credit_transactions_order_id_orders_id_fk" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "feedback" ADD CONSTRAINT "feedback_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "feedback" ADD CONSTRAINT "feedback_resolved_by_users_id_fk" FOREIGN KEY ("resolved_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_accepted_by_users_id_fk" FOREIGN KEY ("accepted_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_affiliate_id_affiliates_id_fk" FOREIGN KEY ("affiliate_id") REFERENCES "public"."affiliates"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_invited_by_users_id_fk" FOREIGN KEY ("invited_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "accounts_provider_user_idx" ON "accounts" USING btree ("provider","provider_user_id");--> statement-breakpoint
CREATE INDEX "memberships_user_idx" ON "accounts_memberships" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "memberships_role_idx" ON "accounts_memberships" USING btree ("role_id");--> statement-breakpoint
CREATE INDEX "affiliates_user_idx" ON "affiliates" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "affiliates_code_idx" ON "affiliates" USING btree ("code");--> statement-breakpoint
CREATE INDEX "affiliates_active_idx" ON "affiliates" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "api_keys_user_idx" ON "api_keys" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "api_keys_hash_idx" ON "api_keys" USING btree ("key_hash");--> statement-breakpoint
CREATE INDEX "api_keys_active_idx" ON "api_keys" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "billing_customers_user_idx" ON "billing_customers" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "billing_customers_provider_customer_idx" ON "billing_customers" USING btree ("provider","customer_id");--> statement-breakpoint
CREATE INDEX "credit_transactions_user_idx" ON "credit_transactions" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "credit_transactions_type_idx" ON "credit_transactions" USING btree ("type");--> statement-breakpoint
CREATE INDEX "credit_transactions_order_idx" ON "credit_transactions" USING btree ("order_id");--> statement-breakpoint
CREATE INDEX "credit_transactions_created_idx" ON "credit_transactions" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "feedback_user_idx" ON "feedback" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "feedback_type_idx" ON "feedback" USING btree ("type");--> statement-breakpoint
CREATE INDEX "feedback_status_idx" ON "feedback" USING btree ("status");--> statement-breakpoint
CREATE INDEX "feedback_priority_idx" ON "feedback" USING btree ("priority");--> statement-breakpoint
CREATE INDEX "feedback_created_idx" ON "feedback" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "feedback_email_idx" ON "feedback" USING btree ("email");--> statement-breakpoint
CREATE INDEX "invitations_token_idx" ON "invitations" USING btree ("token");--> statement-breakpoint
CREATE INDEX "invitations_expires_idx" ON "invitations" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "notifications_user_idx" ON "notifications" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "notifications_channel_idx" ON "notifications" USING btree ("channel");--> statement-breakpoint
CREATE INDEX "notifications_read_idx" ON "notifications" USING btree ("read_at");--> statement-breakpoint
CREATE INDEX "notifications_expires_idx" ON "notifications" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "notifications_created_idx" ON "notifications" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "order_items_order_idx" ON "order_items" USING btree ("order_id");--> statement-breakpoint
CREATE INDEX "order_items_product_idx" ON "order_items" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "orders_user_idx" ON "orders" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "orders_affiliate_idx" ON "orders" USING btree ("affiliate_id");--> statement-breakpoint
CREATE INDEX "orders_number_idx" ON "orders" USING btree ("order_number");--> statement-breakpoint
CREATE INDEX "orders_provider_idx" ON "orders" USING btree ("provider");--> statement-breakpoint
CREATE INDEX "orders_user_email_idx" ON "orders" USING btree ("user_email");--> statement-breakpoint
CREATE INDEX "posts_slug_idx" ON "posts" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "posts_author_idx" ON "posts" USING btree ("author_id");--> statement-breakpoint
CREATE INDEX "posts_published_at_idx" ON "posts" USING btree ("published_at");--> statement-breakpoint
CREATE INDEX "subscription_items_subscription_idx" ON "subscription_items" USING btree ("subscription_id");--> statement-breakpoint
CREATE INDEX "subscription_items_item_idx" ON "subscription_items" USING btree ("subscription_item_id");--> statement-breakpoint
CREATE INDEX "subscription_items_price_idx" ON "subscription_items" USING btree ("price_id");--> statement-breakpoint
CREATE INDEX "subscription_items_product_idx" ON "subscription_items" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "subscriptions_user_idx" ON "subscriptions" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "subscriptions_subscription_idx" ON "subscriptions" USING btree ("subscription_id");--> statement-breakpoint
CREATE INDEX "subscriptions_period_end_idx" ON "subscriptions" USING btree ("current_period_end");--> statement-breakpoint
CREATE INDEX "users_invited_by_idx" ON "users" USING btree ("invited_by");--> statement-breakpoint
CREATE INDEX "users_signin_provider_idx" ON "users" USING btree ("signin_provider");--> statement-breakpoint
CREATE INDEX "users_signin_openid_idx" ON "users" USING btree ("signin_openid");--> statement-breakpoint
CREATE INDEX "users_google_sub_idx" ON "users" USING btree ("google_sub");--> statement-breakpoint
ALTER TABLE "accounts" DROP COLUMN "name";--> statement-breakpoint
ALTER TABLE "accounts" DROP COLUMN "slug";--> statement-breakpoint
ALTER TABLE "accounts" DROP COLUMN "email";--> statement-breakpoint
ALTER TABLE "accounts" DROP COLUMN "picture_url";--> statement-breakpoint
ALTER TABLE "accounts" DROP COLUMN "is_personal_account";--> statement-breakpoint
ALTER TABLE "accounts" DROP COLUMN "primary_owner_user_id";--> statement-breakpoint
ALTER TABLE "accounts" DROP COLUMN "public_data";--> statement-breakpoint
ALTER TABLE "accounts" DROP COLUMN "created_by";--> statement-breakpoint
ALTER TABLE "accounts" DROP COLUMN "updated_by";--> statement-breakpoint
ALTER TABLE "accounts_memberships" DROP COLUMN "account_role";--> statement-breakpoint
ALTER TABLE "accounts_memberships" DROP COLUMN "created_by";--> statement-breakpoint
ALTER TABLE "accounts_memberships" DROP COLUMN "updated_by";--> statement-breakpoint
ALTER TABLE "affiliates" DROP COLUMN "user_uuid";--> statement-breakpoint
ALTER TABLE "affiliates" DROP COLUMN "invited_by";--> statement-breakpoint
ALTER TABLE "affiliates" DROP COLUMN "paid_order_no";--> statement-breakpoint
ALTER TABLE "affiliates" DROP COLUMN "paid_amount";--> statement-breakpoint
ALTER TABLE "affiliates" DROP COLUMN "reward_percent";--> statement-breakpoint
ALTER TABLE "affiliates" DROP COLUMN "reward_amount";--> statement-breakpoint
ALTER TABLE "affiliates" DROP COLUMN "status";--> statement-breakpoint
ALTER TABLE "api_keys" DROP COLUMN "api_key";--> statement-breakpoint
ALTER TABLE "api_keys" DROP COLUMN "title";--> statement-breakpoint
ALTER TABLE "api_keys" DROP COLUMN "user_uuid";--> statement-breakpoint
ALTER TABLE "api_keys" DROP COLUMN "account_id";--> statement-breakpoint
ALTER TABLE "api_keys" DROP COLUMN "status";--> statement-breakpoint
ALTER TABLE "billing_customers" DROP COLUMN "account_id";--> statement-breakpoint
ALTER TABLE "credit_transactions" DROP COLUMN "user_uuid";--> statement-breakpoint
ALTER TABLE "credit_transactions" DROP COLUMN "account_id";--> statement-breakpoint
ALTER TABLE "credit_transactions" DROP COLUMN "trans_type";--> statement-breakpoint
ALTER TABLE "credit_transactions" DROP COLUMN "credits";--> statement-breakpoint
ALTER TABLE "credit_transactions" DROP COLUMN "order_no";--> statement-breakpoint
ALTER TABLE "credit_transactions" DROP COLUMN "expires_at";--> statement-breakpoint
ALTER TABLE "feedback" DROP COLUMN "user_uuid";--> statement-breakpoint
ALTER TABLE "feedback" DROP COLUMN "content";--> statement-breakpoint
ALTER TABLE "feedback" DROP COLUMN "rating";--> statement-breakpoint
ALTER TABLE "invitations" DROP COLUMN "account_id";--> statement-breakpoint
ALTER TABLE "invitations" DROP COLUMN "role";--> statement-breakpoint
ALTER TABLE "invitations" DROP COLUMN "invite_token";--> statement-breakpoint
ALTER TABLE "notifications" DROP COLUMN "account_id";--> statement-breakpoint
ALTER TABLE "order_items" DROP COLUMN "variant_id";--> statement-breakpoint
ALTER TABLE "order_items" DROP COLUMN "price_amount";--> statement-breakpoint
ALTER TABLE "order_items" DROP COLUMN "credits";--> statement-breakpoint
ALTER TABLE "orders" DROP COLUMN "account_id";--> statement-breakpoint
ALTER TABLE "orders" DROP COLUMN "billing_customer_id";--> statement-breakpoint
ALTER TABLE "orders" DROP COLUMN "total_amount";--> statement-breakpoint
ALTER TABLE "orders" DROP COLUMN "order_detail";--> statement-breakpoint
ALTER TABLE "role_permissions" DROP COLUMN "id";--> statement-breakpoint
ALTER TABLE "role_permissions" DROP COLUMN "role";--> statement-breakpoint
ALTER TABLE "roles" DROP COLUMN "hierarchy_level";--> statement-breakpoint
ALTER TABLE "subscription_items" DROP COLUMN "variant_id";--> statement-breakpoint
ALTER TABLE "subscription_items" DROP COLUMN "price_amount";--> statement-breakpoint
ALTER TABLE "subscription_items" DROP COLUMN "interval";--> statement-breakpoint
ALTER TABLE "subscription_items" DROP COLUMN "interval_count";--> statement-breakpoint
ALTER TABLE "subscription_items" DROP COLUMN "type";--> statement-breakpoint
ALTER TABLE "subscriptions" DROP COLUMN "account_id";--> statement-breakpoint
ALTER TABLE "subscriptions" DROP COLUMN "billing_customer_id";--> statement-breakpoint
ALTER TABLE "subscriptions" DROP COLUMN "billing_provider";--> statement-breakpoint
ALTER TABLE "subscriptions" DROP COLUMN "trial_starts_at";--> statement-breakpoint
ALTER TABLE "subscriptions" DROP COLUMN "trial_ends_at";--> statement-breakpoint
ALTER TABLE "users" DROP COLUMN "locale";--> statement-breakpoint
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_user_id_provider_unique" UNIQUE("user_id","provider");--> statement-breakpoint
ALTER TABLE "accounts_memberships" ADD CONSTRAINT "accounts_memberships_account_id_user_id_unique" UNIQUE("account_id","user_id");--> statement-breakpoint
ALTER TABLE "affiliates" ADD CONSTRAINT "affiliates_code_unique" UNIQUE("code");--> statement-breakpoint
ALTER TABLE "api_keys" ADD CONSTRAINT "api_keys_key_hash_unique" UNIQUE("key_hash");--> statement-breakpoint
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_token_unique" UNIQUE("token");--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_order_number_unique" UNIQUE("order_number");--> statement-breakpoint
ALTER TABLE "posts" ADD CONSTRAINT "posts_slug_unique" UNIQUE("slug");--> statement-breakpoint
ALTER TABLE "roles" ADD CONSTRAINT "roles_name_unique" UNIQUE("name");--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_google_sub_unique" UNIQUE("google_sub");