<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .iframe-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        iframe {
            width: 100%;
            height: 800px;
            border: none;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-link {
            display: block;
            padding: 15px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            text-align: center;
            transition: background 0.2s;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .test-link.secondary {
            background: #6c757d;
        }
        .test-link.secondary:hover {
            background: #545b62;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "☐ ";
            margin-right: 8px;
            color: #666;
        }
        .checklist li.checked:before {
            content: "✅ ";
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Google One Tap 登录页面测试</h1>
        
        <div class="test-section">
            <h2>📋 测试清单</h2>
            <ul class="checklist">
                <li id="check-server">开发服务器运行在 http://localhost:5173</li>
                <li id="check-google-console">Google Console 配置完成</li>
                <li id="check-client-id">Client ID 正确配置</li>
                <li id="check-origins">授权的 JavaScript 来源已添加</li>
                <li id="check-redirects">重定向 URI 已配置</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔗 快速测试链接</h2>
            <div class="test-links">
                <a href="http://localhost:5173/auth/login" target="_blank" class="test-link">
                    🔐 登录页面
                </a>
                <a href="http://localhost:5173/test/simple-google" target="_blank" class="test-link">
                    🧪 简化测试
                </a>
                <a href="http://localhost:5173/test/google-auth" target="_blank" class="test-link">
                    🔍 详细调试
                </a>
                <a href="http://localhost:5173/debug/google-config" target="_blank" class="test-link secondary">
                    ⚙️ 配置检查
                </a>
                <a href="http://localhost:5173/fix/google-auth" target="_blank" class="test-link secondary">
                    🛠️ 自动修复
                </a>
                <a href="https://console.developers.google.com/apis/credentials" target="_blank" class="test-link secondary">
                    🌐 Google Console
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>📱 登录页面预览</h2>
            <div class="iframe-container">
                <iframe src="http://localhost:5173/auth/login" title="登录页面"></iframe>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 Google Console 配置</h2>
            <div class="status warning">
                <strong>重要：</strong> 确保在 Google Console 中正确配置以下设置
            </div>
            
            <h3>授权的 JavaScript 来源：</h3>
            <div class="code-block">
http://localhost:5173
http://localhost:3000
http://127.0.0.1:5173
http://127.0.0.1:3000
            </div>

            <h3>授权的重定向 URI：</h3>
            <div class="code-block">
http://localhost:5173/auth/google/callback
http://localhost:5173/auth/google/oauth
http://localhost:3000/auth/google/callback
http://localhost:3000/auth/google/oauth
            </div>

            <h3>当前 Client ID：</h3>
            <div class="code-block" id="client-id">
982969788186-76f0ealh48g2pnhomp01ftescge62hgg.apps.googleusercontent.com
            </div>
        </div>

        <div class="test-section">
            <h2>🐛 常见问题解决</h2>
            <div class="status error">
                <strong>如果 Google One Tap 不显示：</strong>
                <ul>
                    <li>检查 Google Console 配置是否正确</li>
                    <li>等待配置生效（最多 24 小时）</li>
                    <li>清除浏览器缓存和数据</li>
                    <li>使用无痕模式测试</li>
                    <li>确保已登录 Google 账户</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 实时状态检查</h2>
            <div id="status-container">
                <div class="status" id="server-status">检查服务器状态...</div>
                <div class="status" id="google-script-status">检查 Google 脚本加载...</div>
                <div class="status" id="console-status">检查控制台错误...</div>
            </div>
        </div>
    </div>

    <script>
        // 检查服务器状态
        async function checkServerStatus() {
            const statusEl = document.getElementById('server-status');
            try {
                const response = await fetch('http://localhost:5173/auth/login', { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                statusEl.textContent = '✅ 开发服务器运行正常';
                statusEl.className = 'status success';
                document.getElementById('check-server').classList.add('checked');
            } catch (error) {
                statusEl.textContent = '❌ 无法连接到开发服务器 (http://localhost:5173)';
                statusEl.className = 'status error';
            }
        }

        // 检查 Google 脚本
        function checkGoogleScript() {
            const statusEl = document.getElementById('google-script-status');
            const script = document.createElement('script');
            script.src = 'https://accounts.google.com/gsi/client';
            script.onload = () => {
                statusEl.textContent = '✅ Google GSI 脚本加载成功';
                statusEl.className = 'status success';
            };
            script.onerror = () => {
                statusEl.textContent = '❌ Google GSI 脚本加载失败';
                statusEl.className = 'status error';
            };
            document.head.appendChild(script);
        }

        // 监听控制台错误
        function monitorConsoleErrors() {
            const statusEl = document.getElementById('console-status');
            const originalError = console.error;
            let errorCount = 0;
            
            console.error = function(...args) {
                errorCount++;
                statusEl.textContent = `⚠️ 发现 ${errorCount} 个控制台错误`;
                statusEl.className = 'status warning';
                originalError.apply(console, args);
            };
            
            // 初始状态
            setTimeout(() => {
                if (errorCount === 0) {
                    statusEl.textContent = '✅ 暂无控制台错误';
                    statusEl.className = 'status success';
                }
            }, 3000);
        }

        // 页面加载时运行检查
        document.addEventListener('DOMContentLoaded', () => {
            checkServerStatus();
            checkGoogleScript();
            monitorConsoleErrors();
        });

        // 定期检查服务器状态
        setInterval(checkServerStatus, 30000);
    </script>
</body>
</html>
