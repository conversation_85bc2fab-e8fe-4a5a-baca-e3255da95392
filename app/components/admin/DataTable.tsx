import { useSearchParams } from "@remix-run/react";
import { type ReactNode, useState } from "react";
import { Pagination, type PaginationInfo } from "./Pagination";

export interface TableColumn<T = Record<string, unknown>> {
  key: string;
  label: string;
  sortable?: boolean;
  render?: (value: unknown, row: T, index: number) => ReactNode;
  className?: string;
  headerClassName?: string;
}

export interface DataTableProps<T = Record<string, unknown>> {
  columns: TableColumn<T>[];
  data: T[];
  pagination?: PaginationInfo;
  loading?: boolean;
  emptyMessage?: string;
  onSort?: (sortBy: string, sortOrder: "asc" | "desc") => void;
  onPageChange?: (page: number) => void;
  className?: string;
  rowClassName?: string | ((row: T, index: number) => string);
  showPagination?: boolean;
}

export function DataTable<T = Record<string, unknown>>({
  columns,
  data,
  pagination,
  loading = false,
  emptyMessage = "No data available",
  onSort,
  onPageChange,
  className = "",
  rowClassName = "",
  showPagination = true,
}: DataTableProps<T>) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [sortBy, setSortBy] = useState(searchParams.get("sortBy") || "");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">(
    (searchParams.get("sortOrder") as "asc" | "desc") || "desc"
  );

  const handleSort = (columnKey: string) => {
    const newSortOrder = sortBy === columnKey && sortOrder === "desc" ? "asc" : "desc";
    setSortBy(columnKey);
    setSortOrder(newSortOrder);

    // Update URL params
    const newParams = new URLSearchParams(searchParams);
    newParams.set("sortBy", columnKey);
    newParams.set("sortOrder", newSortOrder);
    newParams.set("page", "1"); // Reset to first page
    setSearchParams(newParams);

    if (onSort) {
      onSort(columnKey, newSortOrder);
    }
  };

  const getSortIcon = (columnKey: string) => {
    if (sortBy !== columnKey) {
      return (
        <svg
          className="w-4 h-4 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
          />
        </svg>
      );
    }

    if (sortOrder === "asc") {
      return (
        <svg
          className="w-4 h-4 text-blue-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"
          />
        </svg>
      );
    }

    return (
      <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4"
        />
      </svg>
    );
  };

  const getRowClassName = (row: T, index: number) => {
    if (typeof rowClassName === "function") {
      return rowClassName(row, index);
    }
    return rowClassName;
  };

  const renderCellContent = (column: TableColumn<T>, row: T, index: number): ReactNode => {
    const value = (row as Record<string, unknown>)[column.key];

    if (column.render) {
      return column.render(value, row, index);
    }

    // Convert unknown value to ReactNode safely
    if (value === null || value === undefined) {
      return "-";
    }

    if (typeof value === "string" || typeof value === "number" || typeof value === "boolean") {
      return String(value);
    }

    if (value instanceof Date) {
      return value.toLocaleDateString();
    }

    // For complex objects, stringify them
    return JSON.stringify(value);
  };

  return (
    <div className={`bg-white rounded-lg shadow overflow-hidden ${className}`}>
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                    column.headerClassName || ""
                  }`}
                >
                  {column.sortable ? (
                    <button
                      type="button"
                      onClick={() => handleSort(column.key)}
                      className="flex items-center space-x-1 hover:text-gray-700 focus:outline-none"
                    >
                      <span>{column.label}</span>
                      {getSortIcon(column.key)}
                    </button>
                  ) : (
                    column.label
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-12 text-center">
                  <div className="flex justify-center items-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
                    <span className="ml-2 text-gray-500">Loading...</span>
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-12 text-center">
                  <div className="text-gray-500">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400 mb-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
                      />
                    </svg>
                    <p>{emptyMessage}</p>
                  </div>
                </td>
              </tr>
            ) : (
              data.map((row, index) => (
                <tr
                  key={`row-${index}-${JSON.stringify(row).slice(0, 20)}`}
                  className={`hover:bg-gray-50 ${getRowClassName(row, index)}`}
                >
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={`px-6 py-4 whitespace-nowrap ${column.className || ""}`}
                    >
                      {renderCellContent(column, row, index)}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {showPagination && pagination && (
        <div className="px-6 py-4 border-t border-gray-200">
          <Pagination pagination={pagination} onPageChange={onPageChange} />
        </div>
      )}
    </div>
  );
}

// Utility function to create common column types
export const createColumn = {
  text: <T,>(key: keyof T, label: string, options?: Partial<TableColumn<T>>): TableColumn<T> => ({
    key: key as string,
    label,
    sortable: true,
    ...options,
  }),

  number: <T,>(key: keyof T, label: string, options?: Partial<TableColumn<T>>): TableColumn<T> => ({
    key: key as string,
    label,
    sortable: true,
    render: (value): ReactNode => {
      if (typeof value === "number") {
        return value.toLocaleString();
      }
      return String(value ?? "-");
    },
    className: "text-right",
    ...options,
  }),

  currency: <T,>(
    key: keyof T,
    label: string,
    currency = "USD",
    options?: Partial<TableColumn<T>>
  ): TableColumn<T> => ({
    key: key as string,
    label,
    sortable: true,
    render: (value): ReactNode => {
      if (value === null || value === undefined) {
        return "-";
      }
      const num = parseFloat(String(value));
      return Number.isNaN(num)
        ? String(value)
        : new Intl.NumberFormat("en-US", {
            style: "currency",
            currency,
          }).format(num);
    },
    className: "text-right",
    ...options,
  }),

  date: <T,>(key: keyof T, label: string, options?: Partial<TableColumn<T>>): TableColumn<T> => ({
    key: key as string,
    label,
    sortable: true,
    render: (value): ReactNode => {
      if (!value) return "-";
      const date = new Date(String(value));
      return Number.isNaN(date.getTime())
        ? String(value)
        : date.toLocaleDateString(undefined, { year: "numeric", month: "long", day: "numeric" });
    },
    ...options,
  }),

  badge: <T,>(
    key: keyof T,
    label: string,
    colorMap: Record<string, string>,
    options?: Partial<TableColumn<T>>
  ): TableColumn<T> => ({
    key: key as string,
    label,
    render: (value): ReactNode => {
      const valueStr = String(value ?? "");
      const color = colorMap[valueStr] || "bg-gray-100 text-gray-800";
      return (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${color}`}>
          {valueStr}
        </span>
      );
    },
    ...options,
  }),

  actions: <T,>(
    label: string,
    actions: Array<{
      label: string;
      onClick: (row: T, index: number) => void;
      className?: string;
      show?: (row: T, index: number) => boolean;
    }>,
    options?: Partial<TableColumn<T>>
  ): TableColumn<T> => ({
    key: "actions",
    label,
    render: (_, row, index) => (
      <div className="flex space-x-2">
        {actions.map((action, actionIndex) => {
          if (action.show && !action.show(row, index)) {
            return null;
          }

          return (
            <button
              type="button"
              key={`action-${action.label}-${actionIndex}`}
              onClick={() => action.onClick(row, index)}
              className={`text-sm font-medium hover:underline ${
                action.className || "text-blue-600 hover:text-blue-900"
              }`}
            >
              {action.label}
            </button>
          );
        })}
      </div>
    ),
    ...options,
  }),
};
