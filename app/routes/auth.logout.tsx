/**
 * Logout Route
 * Handles user logout using Remix Auth
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { authenticator } from "~/lib/auth/remix-auth.server";

export async function action({ request }: ActionFunctionArgs) {
  return await authenticator.logout(request, { redirectTo: "/auth/login" });
}

export async function loader({ request }: LoaderFunctionArgs) {
  return await authenticator.logout(request, { redirectTo: "/auth/login" });
}

// This component should never render as the loader/action redirects
export default function Logout() {
  return <div>Logging out...</div>;
}
