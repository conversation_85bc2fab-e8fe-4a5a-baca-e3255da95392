/**
 * Email/Password Sign Up Route
 * Handles user registration with email and password
 */

import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { authenticator } from "~/lib/auth/remix-auth.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Sign Up - AI SaaS Starter" },
    { name: "description", content: "Create your account" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Check if user is already authenticated
  const user = await authenticator.isAuthenticated(request);
  if (user) {
    return redirect("/console");
  }

  return json({
    googleClientId: process.env.GOOGLE_CLIENT_ID,
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;
  const name = formData.get("name") as string;

  // Basic validation
  const errors: Record<string, string> = {};

  if (!email) {
    errors.email = "Email is required";
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.email = "Invalid email format";
  }

  if (!password) {
    errors.password = "Password is required";
  } else if (password.length < 8) {
    errors.password = "Password must be at least 8 characters long";
  }

  if (password !== confirmPassword) {
    errors.confirmPassword = "Passwords do not match";
  }

  if (!name) {
    errors.name = "Name is required";
  }

  if (Object.keys(errors).length > 0) {
    return json({ errors }, { status: 400 });
  }

  try {
    // Add action field to form data for the authenticator
    const signupFormData = new FormData();
    signupFormData.append("email", email);
    signupFormData.append("password", password);
    signupFormData.append("name", name);
    signupFormData.append("action", "signup");

    // Create a new request with the form data
    const signupRequest = new Request(request.url, {
      method: "POST",
      body: signupFormData,
      headers: request.headers,
    });

    return await authenticator.authenticate("form", signupRequest, {
      successRedirect: "/console",
      failureRedirect: "/auth/signup",
    });
  } catch (error) {
    console.error("Signup error:", error);

    if (error instanceof Error) {
      return json(
        {
          errors: { general: error.message },
        },
        { status: 400 }
      );
    }

    return json(
      {
        errors: { general: "An unexpected error occurred" },
      },
      { status: 500 }
    );
  }
}

export default function SignUpPage() {
  const { googleClientId } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  return (
    <UnifiedLayout showHeader={false} showFooter={false} showSidebar={false} containerSize="full">
      {/* Background Effects */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl animate-pulse" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-gradient-to-r from-purple-500/15 to-pink-500/15 rounded-full blur-2xl animate-pulse delay-1000" />
        <div className="absolute bottom-40 left-1/4 w-24 h-24 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full blur-xl animate-pulse delay-2000" />
      </div>

      <section className="py-32 relative overflow-hidden min-h-screen flex items-center">
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-md mx-auto">
            {/* Header */}
            <div className="text-center mb-12 animate-fade-in-up">
              <h1 className="text-4xl font-extrabold text-gray-900 dark:text-white mb-4 tracking-tight">
                Create Your Account
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 font-medium">
                Join our AI workspace platform
              </p>
            </div>

            {/* Auth Card */}
            <div className="group relative animate-fade-in-up delay-200">
              <div className="absolute -inset-1 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-cyan-600/20 rounded-3xl blur opacity-30 group-hover:opacity-50 transition duration-1000" />
              <Card className="relative shadow-2xl border-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-3xl overflow-hidden">
                <CardHeader className="text-center pb-6 pt-8">
                  <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                    Sign Up
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-300">
                    Create your account with email and password
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-8 pb-8">
                  {/* Google Sign Up Option */}
                  {googleClientId && (
                    <>
                      <Link
                        to="/auth/google"
                        className="w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                      >
                        <svg className="w-5 h-5" viewBox="0 0 24 24">
                          <path
                            fill="currentColor"
                            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                          />
                          <path
                            fill="currentColor"
                            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                          />
                          <path
                            fill="currentColor"
                            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                          />
                          <path
                            fill="currentColor"
                            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                          />
                        </svg>
                        Continue with Google
                      </Link>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <div className="w-full border-t border-gray-300 dark:border-gray-600" />
                        </div>
                        <div className="relative flex justify-center text-sm">
                          <span className="px-2 bg-white dark:bg-gray-900 text-gray-500">Or</span>
                        </div>
                      </div>
                    </>
                  )}

                  {/* Error Messages */}
                  {actionData?.errors?.general && (
                    <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                      <p className="text-sm text-red-600 dark:text-red-400">
                        {actionData.errors.general}
                      </p>
                    </div>
                  )}

                  {/* Sign Up Form */}
                  <Form method="post" className="space-y-4">
                    <div>
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        required
                        className="mt-1"
                        placeholder="Enter your full name"
                      />
                      {actionData?.errors?.name && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                          {actionData.errors.name}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        required
                        className="mt-1"
                        placeholder="Enter your email"
                      />
                      {actionData?.errors?.email && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                          {actionData.errors.email}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="password">Password</Label>
                      <Input
                        id="password"
                        name="password"
                        type="password"
                        required
                        className="mt-1"
                        placeholder="Create a password"
                      />
                      {actionData?.errors?.password && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                          {actionData.errors.password}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="confirmPassword">Confirm Password</Label>
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        required
                        className="mt-1"
                        placeholder="Confirm your password"
                      />
                      {actionData?.errors?.confirmPassword && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                          {actionData.errors.confirmPassword}
                        </p>
                      )}
                    </div>

                    <Button type="submit" className="w-full" disabled={isSubmitting}>
                      {isSubmitting ? "Creating Account..." : "Create Account"}
                    </Button>
                  </Form>

                  {/* Terms */}
                  <div className="text-center text-xs text-gray-500 dark:text-gray-400 pt-4 border-t border-gray-200 dark:border-gray-700">
                    By signing up, you agree to our{" "}
                    <Link
                      to="/legal/terms"
                      className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                    >
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link
                      to="/legal/privacy"
                      className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                    >
                      Privacy Policy
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sign In Link */}
            <div className="text-center mt-8 animate-fade-in-up delay-400">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Already have an account?{" "}
                <Link
                  to="/auth/login"
                  className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                >
                  Sign in here
                </Link>
              </p>
            </div>
          </div>
        </div>
      </section>
    </UnifiedLayout>
  );
}
