/**
 * Email/Password Login Route
 * Handles user authentication with email and password
 */

import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { authenticator } from "~/lib/auth/remix-auth.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Email Login - AI SaaS Starter" },
    { name: "description", content: "Sign in with your email and password" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Check if user is already authenticated
  const user = await authenticator.isAuthenticated(request);
  if (user) {
    return redirect("/console");
  }

  return json({});
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;

  // Basic validation
  const errors: Record<string, string> = {};

  if (!email) {
    errors.email = "Email is required";
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.email = "Invalid email format";
  }

  if (!password) {
    errors.password = "Password is required";
  }

  if (Object.keys(errors).length > 0) {
    return json({ errors }, { status: 400 });
  }

  try {
    // Add action field to form data for the authenticator
    const loginFormData = new FormData();
    loginFormData.append("email", email);
    loginFormData.append("password", password);
    loginFormData.append("action", "login");

    // Create a new request with the form data
    const loginRequest = new Request(request.url, {
      method: "POST",
      body: loginFormData,
      headers: request.headers,
    });

    return await authenticator.authenticate("form", loginRequest, {
      successRedirect: "/console",
      failureRedirect: "/auth/email-login",
    });
  } catch (error) {
    console.error("Login error:", error);

    if (error instanceof Error) {
      return json(
        {
          errors: { general: error.message },
        },
        { status: 400 }
      );
    }

    return json(
      {
        errors: { general: "An unexpected error occurred" },
      },
      { status: 500 }
    );
  }
}

export default function EmailLoginPage() {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  return (
    <UnifiedLayout showHeader={false} showFooter={false} showSidebar={false} containerSize="full">
      {/* Background Effects */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl animate-pulse" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-gradient-to-r from-purple-500/15 to-pink-500/15 rounded-full blur-2xl animate-pulse delay-1000" />
        <div className="absolute bottom-40 left-1/4 w-24 h-24 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full blur-xl animate-pulse delay-2000" />
      </div>

      <section className="py-32 relative overflow-hidden min-h-screen flex items-center">
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-md mx-auto">
            {/* Header */}
            <div className="text-center mb-12 animate-fade-in-up">
              <h1 className="text-4xl font-extrabold text-gray-900 dark:text-white mb-4 tracking-tight">
                Sign In with Email
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 font-medium">
                Enter your email and password to continue
              </p>
            </div>

            {/* Auth Card */}
            <div className="group relative animate-fade-in-up delay-200">
              <div className="absolute -inset-1 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-cyan-600/20 rounded-3xl blur opacity-30 group-hover:opacity-50 transition duration-1000" />
              <Card className="relative shadow-2xl border-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-3xl overflow-hidden">
                <CardHeader className="text-center pb-6 pt-8">
                  <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                    Email Login
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-300">
                    Sign in to your account
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-8 pb-8">
                  {/* Error Messages */}
                  {actionData?.errors?.general && (
                    <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                      <p className="text-sm text-red-600 dark:text-red-400">
                        {actionData.errors.general}
                      </p>
                    </div>
                  )}

                  {/* Login Form */}
                  <Form method="post" className="space-y-4">
                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        required
                        className="mt-1"
                        placeholder="Enter your email"
                      />
                      {actionData?.errors?.email && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                          {actionData.errors.email}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="password">Password</Label>
                      <Input
                        id="password"
                        name="password"
                        type="password"
                        required
                        className="mt-1"
                        placeholder="Enter your password"
                      />
                      {actionData?.errors?.password && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                          {actionData.errors.password}
                        </p>
                      )}
                    </div>

                    <Button type="submit" className="w-full" disabled={isSubmitting}>
                      {isSubmitting ? "Signing In..." : "Sign In"}
                    </Button>
                  </Form>

                  {/* Back to main login */}
                  <div className="text-center">
                    <Link
                      to="/auth/login"
                      className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                    >
                      ← Back to main login
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sign Up Link */}
            <div className="text-center mt-8 animate-fade-in-up delay-400">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Don't have an account?{" "}
                <Link
                  to="/auth/signup"
                  className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                >
                  Sign up here
                </Link>
              </p>
            </div>
          </div>
        </div>
      </section>
    </UnifiedLayout>
  );
}
