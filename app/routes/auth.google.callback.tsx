/**
 * Google OAuth Callback Route
 * Handles both OAuth callback and One Tap credential
 */

import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticator } from "~/lib/auth/remix-auth.server";

export async function loader({ request }: LoaderFunctionArgs) {
  // Handle regular OAuth callback
  return authenticator.authenticate("google", request, {
    successRedirect: "/console",
    failureRedirect: "/auth/login?error=google-oauth-failed",
  });
}

export async function action({ request }: ActionFunctionArgs) {
  // Handle Google One Tap credential submission
  const formData = await request.formData();
  const credential = formData.get("credential") as string;
  
  if (!credential) {
    return json({ error: "No credential provided" }, { status: 400 });
  }
  
  try {
    // For now, redirect to regular Google OAuth flow
    // TODO: Implement One Tap credential verification
    console.log("One Tap credential received, redirecting to OAuth flow");
    
    // Redirect to Google OAuth with a flag that it came from One Tap
    return Response.redirect("/auth/google?source=one-tap", 302);
  } catch (error) {
    console.error("One Tap authentication failed:", error);
    return json({ error: "Authentication failed" }, { status: 400 });
  }
}

// This component should never render as the loader redirects
export default function GoogleCallback() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4" />
        <p className="text-gray-600">Processing Google authentication...</p>
      </div>
    </div>
  );
}
