import { neon } from "@neondatabase/serverless";
import { drizzle, type NeonHttpDatabase } from "drizzle-orm/neon-http";
import type { PgTransaction } from "drizzle-orm/pg-core";
import {
  getNeonOptimizedConfig,
  NeonConnectionManager,
  NeonQueryMonitor,
} from "../neon/optimization";
import { getVercelEnvironment } from "../vercel/environment";
import * as schema from "./schema";

// Database configuration interface
export interface DatabaseConfig {
  url: string;
  maxConnections?: number;
  connectionTimeoutMillis?: number;
  idleTimeoutMillis?: number;
  enableLogging?: boolean;
}

// Default configuration
const DEFAULT_CONFIG: Partial<DatabaseConfig> = {
  maxConnections: 10,
  connectionTimeoutMillis: 30000, // 30 seconds
  idleTimeoutMillis: 600000, // 10 minutes
  enableLogging: false, // Default to false, will be overridden by environment-specific config
};

// Custom logger for database operations
class DatabaseLogger {
  private enabled: boolean;

  constructor(enabled = false) {
    this.enabled = enabled;
  }

  logQuery(query: string, params: unknown[]) {
    if (this.enabled) {
      console.log("🔍 Database Query:", query);
      if (params.length > 0) {
        console.log("📋 Parameters:", params);
      }
    }
  }
}

// Enhanced database creation function
export function createDb(config: DatabaseConfig | string) {
  let dbConfig: DatabaseConfig;

  // Handle both string URL and config object
  if (typeof config === "string") {
    dbConfig = { url: config, ...DEFAULT_CONFIG };
  } else {
    dbConfig = { ...DEFAULT_CONFIG, ...config };
  }

  // Validate database URL
  if (!dbConfig.url) {
    throw new Error("Database URL is required and cannot be undefined, null, or empty.");
  }

  try {
    // Validate URL format
    new URL(dbConfig.url);
  } catch (error) {
    throw new Error(
      `Invalid database URL format: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }

  try {
    // Get optimized configuration for Neon
    const neonConfig = getNeonOptimizedConfig();
    const connectionManager = NeonConnectionManager.getInstance();

    // Create Neon connection with configuration
    const sql = neon(dbConfig.url);

    // Create Drizzle instance with enhanced configuration
    const db = drizzle(sql, {
      schema,
      logger: dbConfig.enableLogging
        ? {
            logQuery: (query: string, params: unknown[]) => {
              const monitor = NeonQueryMonitor.startQuery(query, params);
              // Note: We can't easily track the end time here due to Drizzle's architecture
              // The monitoring is handled in the NeonQueryMonitor class
              if (dbConfig.enableLogging) {
                console.log("🔍 Database Query:", query);
                if (params.length > 0) {
                  console.log("📋 Parameters:", params);
                }
              }
            },
          }
        : false,
    });

    // Add custom methods to the database instance
    return Object.assign(db, {
      // Enhanced health check method
      async healthCheck(): Promise<{
        status: "healthy" | "unhealthy";
        timestamp: Date;
        latency?: number;
        connectionStats?: any;
      }> {
        const startTime = Date.now();
        try {
          await sql`SELECT 1`;
          const latency = Date.now() - startTime;
          connectionManager.trackConnection();

          return {
            status: "healthy",
            timestamp: new Date(),
            latency,
            connectionStats: connectionManager.getStats(),
          };
        } catch (error) {
          console.error("Database health check failed:", error);
          return {
            status: "unhealthy",
            timestamp: new Date(),
            latency: Date.now() - startTime,
          };
        }
      },

      // Get database configuration
      getConfig(): DatabaseConfig {
        return dbConfig;
      },

      // Get Neon optimization configuration
      getNeonConfig() {
        return neonConfig;
      },

      // Get connection manager stats
      getConnectionStats() {
        return connectionManager.getStats();
      },
    });
  } catch (error) {
    throw new Error(
      `Failed to create database connection: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
}

// Type for the enhanced database instance
export type Database = ReturnType<typeof createDb>;

// Utility function to create database with environment variables
export function createDbFromEnv(env?: Record<string, string | undefined> | any): Database {
  // In Vercel, process.env is available
  // env parameter is optional for backward compatibility
  const databaseUrl =
    env?.DATABASE_URL ||
    (typeof process !== "undefined" && process.env ? process.env.DATABASE_URL : undefined);

  if (!databaseUrl) {
    throw new Error("DATABASE_URL environment variable is required but not found");
  }

  // Determine if we're in development mode
  const isDevelopment =
    env?.NODE_ENV === "development" ||
    (typeof process !== "undefined" && process.env
      ? process.env.NODE_ENV === "development"
      : false);

  // Determine if we're in Vercel environment
  const isVercel =
    env?.VERCEL === "1" || (typeof process !== "undefined" && process.env?.VERCEL === "1");

  // Optimize configuration for Vercel deployment
  const config: DatabaseConfig = {
    url: databaseUrl,
    enableLogging: isDevelopment,
    // Vercel serverless functions have limited execution time
    connectionTimeoutMillis: isVercel ? 10000 : 30000, // 10s for Vercel, 30s for others
    idleTimeoutMillis: isVercel ? 300000 : 600000, // 5min for Vercel, 10min for others
    maxConnections: isVercel ? 5 : 10, // Fewer connections for serverless
  };

  return createDb(config);
}

// Connection pool manager (for advanced use cases)
export class DatabasePool {
  private static instance: Database | null = null;
  private static config: DatabaseConfig | null = null;

  static getInstance(config?: DatabaseConfig | string): Database {
    if (!DatabasePool.instance || (config && config !== DatabasePool.config)) {
      DatabasePool.instance = createDb(config || DatabasePool.config!);
      DatabasePool.config = typeof config === "string" ? { url: config } : config || null;
    }
    return DatabasePool.instance;
  }

  static async closeConnection(): Promise<void> {
    if (DatabasePool.instance) {
      // Neon serverless connections are automatically managed
      // No explicit close needed, but we can reset the instance
      DatabasePool.instance = null;
      DatabasePool.config = null;
    }
  }
}

// Export commonly used types and classes
export { DatabaseLogger };
