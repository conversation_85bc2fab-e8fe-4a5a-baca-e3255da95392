/**
 * Cache Management System
 *
 * Provides a unified caching interface for the application with support for:
 * - In-memory caching (development)
 * - Redis caching (production)
 * - Browser caching (client-side)
 * - Database query caching
 */

import { getVercelEnvironment } from "../vercel/environment";

export interface CacheConfig {
  defaultTtl: number; // Time to live in seconds
  maxSize: number; // Maximum cache size
  enableCompression: boolean;
  enableMetrics: boolean;
}

export interface CacheEntry<T = any> {
  value: T;
  timestamp: number;
  ttl: number;
  hits: number;
  size: number;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  totalSize: number;
  entryCount: number;
}

/**
 * In-memory cache implementation for development and fallback
 */
export class MemoryCache {
  private cache = new Map<string, CacheEntry>();
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    totalSize: 0,
    entryCount: 0,
  };
  private config: CacheConfig;

  constructor(config: CacheConfig) {
    this.config = config;

    // Clean up expired entries periodically
    setInterval(() => this.cleanup(), 60000); // Every minute
  }

  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);

    if (!entry) {
      this.metrics.misses++;
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.cache.delete(key);
      this.metrics.misses++;
      this.metrics.evictions++;
      this.updateSize();
      return null;
    }

    entry.hits++;
    this.metrics.hits++;
    return entry.value as T;
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const entryTtl = ttl || this.config.defaultTtl;
    const size = this.estimateSize(value);

    // Check if we need to evict entries
    if (this.metrics.totalSize + size > this.config.maxSize) {
      await this.evictLRU();
    }

    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      ttl: entryTtl,
      hits: 0,
      size,
    };

    this.cache.set(key, entry);
    this.metrics.sets++;
    this.updateSize();
  }

  async delete(key: string): Promise<boolean> {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.metrics.deletes++;
      this.updateSize();
    }
    return deleted;
  }

  async clear(): Promise<void> {
    this.cache.clear();
    this.metrics.totalSize = 0;
    this.metrics.entryCount = 0;
  }

  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  private cleanup(): void {
    const now = Date.now();
    let evicted = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl * 1000) {
        this.cache.delete(key);
        evicted++;
      }
    }

    if (evicted > 0) {
      this.metrics.evictions += evicted;
      this.updateSize();
    }
  }

  private async evictLRU(): Promise<void> {
    // Find least recently used entry (lowest hits)
    let lruKey: string | null = null;
    let lruHits = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.hits < lruHits) {
        lruHits = entry.hits;
        lruKey = key;
      }
    }

    if (lruKey) {
      this.cache.delete(lruKey);
      this.metrics.evictions++;
      this.updateSize();
    }
  }

  private updateSize(): void {
    this.metrics.totalSize = Array.from(this.cache.values()).reduce(
      (total, entry) => total + entry.size,
      0
    );
    this.metrics.entryCount = this.cache.size;
  }

  private estimateSize(value: any): number {
    // Rough estimation of object size in bytes
    const str = JSON.stringify(value);
    return str.length * 2; // Approximate UTF-16 encoding
  }
}

/**
 * Cache key generator utilities
 */
export class CacheKeys {
  static user(userId: string): string {
    return `user:${userId}`;
  }

  static userProfile(userId: string): string {
    return `user:profile:${userId}`;
  }

  static userCredits(userId: string): string {
    return `user:credits:${userId}`;
  }

  static order(orderId: string): string {
    return `order:${orderId}`;
  }

  static userOrders(userId: string, page = 1, limit = 10): string {
    return `user:orders:${userId}:${page}:${limit}`;
  }

  static subscription(userId: string): string {
    return `subscription:${userId}`;
  }

  static aiModel(provider: string, model: string): string {
    return `ai:model:${provider}:${model}`;
  }

  static apiResponse(endpoint: string, params: string): string {
    return `api:${endpoint}:${params}`;
  }

  static dbQuery(query: string, params: string): string {
    const hash = CacheKeys.hashString(query + params);
    return `db:query:${hash}`;
  }

  private static hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

/**
 * Cache factory - creates appropriate cache instance based on environment
 */
export class CacheFactory {
  private static instance: MemoryCache | null = null;

  static getInstance(): MemoryCache {
    if (!CacheFactory.instance) {
      const vercelEnv = getVercelEnvironment();

      const config: CacheConfig = {
        defaultTtl: vercelEnv.isProduction ? 3600 : 300, // 1 hour in prod, 5 min in dev
        maxSize: vercelEnv.isVercel ? 50 * 1024 * 1024 : 100 * 1024 * 1024, // 50MB on Vercel, 100MB locally
        enableCompression: vercelEnv.isProduction,
        enableMetrics: true,
      };

      CacheFactory.instance = new MemoryCache(config);
    }

    return CacheFactory.instance;
  }

  static clearInstance(): void {
    CacheFactory.instance = null;
  }
}

/**
 * Cache decorator for functions
 */
export function cached<T extends (...args: any[]) => Promise<any>>(
  keyGenerator: (...args: Parameters<T>) => string,
  ttl?: number
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const cache = CacheFactory.getInstance();

    descriptor.value = async function (...args: Parameters<T>) {
      const key = keyGenerator(...args);

      // Try to get from cache first
      const cached = await cache.get(key);
      if (cached !== null) {
        return cached;
      }

      // Execute original method
      const result = await method.apply(this, args);

      // Cache the result
      await cache.set(key, result, ttl);

      return result;
    };

    return descriptor;
  };
}

/**
 * Default cache instance
 */
export const cache = CacheFactory.getInstance();

/**
 * Cache utilities
 */
export const CacheUtils = {
  /**
   * Warm up cache with frequently accessed data
   */
  async warmUp(): Promise<void> {
    // Implementation would depend on your specific use case
    console.log("Cache warm-up started");
    // Example: Pre-load user data, popular content, etc.
  },

  /**
   * Get cache statistics
   */
  getStats(): CacheMetrics {
    return cache.getMetrics();
  },

  /**
   * Clear all cache entries
   */
  async clearAll(): Promise<void> {
    await cache.clear();
  },

  /**
   * Health check for cache system
   */
  async healthCheck(): Promise<{ status: "healthy" | "unhealthy"; metrics: CacheMetrics }> {
    try {
      const testKey = "health-check";
      const testValue = { timestamp: Date.now() };

      await cache.set(testKey, testValue, 10);
      const retrieved = await cache.get(testKey);
      await cache.delete(testKey);

      const isHealthy = retrieved !== null && retrieved.timestamp === testValue.timestamp;

      return {
        status: isHealthy ? "healthy" : "unhealthy",
        metrics: cache.getMetrics(),
      };
    } catch (error) {
      console.error("Cache health check failed:", error);
      return {
        status: "unhealthy",
        metrics: cache.getMetrics(),
      };
    }
  },
};
