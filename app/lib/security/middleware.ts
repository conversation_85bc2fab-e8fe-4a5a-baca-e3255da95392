/**
 * Security Middleware Collection
 *
 * Provides comprehensive security middleware including:
 * - Rate limiting
 * - CSRF protection
 * - Input validation
 * - Security headers
 * - Authentication checks
 */

import { getVercelEnvironment } from "../vercel/environment";

export interface SecurityConfig {
  enableRateLimit: boolean;
  enableCSRF: boolean;
  enableSecurityHeaders: boolean;
  enableInputValidation: boolean;
  rateLimitRequests: number;
  rateLimitWindow: number;
  trustedOrigins: string[];
}

export interface RateLimitEntry {
  count: number;
  resetTime: number;
  blocked: boolean;
}

/**
 * Rate limiting implementation
 */
export class RateLimiter {
  private requests = new Map<string, RateLimitEntry>();
  private config: SecurityConfig;

  constructor(config: SecurityConfig) {
    this.config = config;

    // Clean up expired entries periodically
    setInterval(() => this.cleanup(), 60000); // Every minute
  }

  /**
   * Check if request should be rate limited
   */
  isRateLimited(identifier: string): boolean {
    if (!this.config.enableRateLimit) {
      return false;
    }

    const now = Date.now();
    const entry = this.requests.get(identifier);

    if (!entry) {
      // First request from this identifier
      this.requests.set(identifier, {
        count: 1,
        resetTime: now + this.config.rateLimitWindow,
        blocked: false,
      });
      return false;
    }

    // Check if window has expired
    if (now > entry.resetTime) {
      // Reset the counter
      entry.count = 1;
      entry.resetTime = now + this.config.rateLimitWindow;
      entry.blocked = false;
      return false;
    }

    // Increment counter
    entry.count++;

    // Check if limit exceeded
    if (entry.count > this.config.rateLimitRequests) {
      entry.blocked = true;
      return true;
    }

    return false;
  }

  /**
   * Get rate limit info for response headers
   */
  getRateLimitInfo(identifier: string) {
    const entry = this.requests.get(identifier);

    if (!entry) {
      return {
        limit: this.config.rateLimitRequests,
        remaining: this.config.rateLimitRequests,
        reset: Date.now() + this.config.rateLimitWindow,
      };
    }

    return {
      limit: this.config.rateLimitRequests,
      remaining: Math.max(0, this.config.rateLimitRequests - entry.count),
      reset: entry.resetTime,
    };
  }

  /**
   * Clean up expired entries
   */
  private cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.requests.entries()) {
      if (now > entry.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

/**
 * CSRF protection
 */
export class CSRFProtection {
  private tokens = new Map<string, { token: string; expires: number }>();
  private config: SecurityConfig;

  constructor(config: SecurityConfig) {
    this.config = config;
  }

  /**
   * Generate CSRF token
   */
  generateToken(sessionId: string): string {
    if (!this.config.enableCSRF) {
      return "";
    }

    const token = this.randomString(32);
    const expires = Date.now() + 3600000; // 1 hour

    this.tokens.set(sessionId, { token, expires });
    return token;
  }

  /**
   * Validate CSRF token
   */
  validateToken(sessionId: string, token: string): boolean {
    if (!this.config.enableCSRF) {
      return true;
    }

    const entry = this.tokens.get(sessionId);
    if (!entry) {
      return false;
    }

    // Check if token has expired
    if (Date.now() > entry.expires) {
      this.tokens.delete(sessionId);
      return false;
    }

    return entry.token === token;
  }

  /**
   * Clean up expired tokens
   */
  cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.tokens.entries()) {
      if (now > entry.expires) {
        this.tokens.delete(key);
      }
    }
  }

  private randomString(length: number): string {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

/**
 * Input validation utilities
 */
export class InputValidator {
  /**
   * Sanitize string input
   */
  static sanitizeString(input: string): string {
    return input
      .replace(/[<>]/g, "") // Remove potential HTML tags
      .replace(/['"]/g, "") // Remove quotes
      .trim();
  }

  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate UUID format
   */
  static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Check for SQL injection patterns
   */
  static hasSQLInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(--|\/\*|\*\/)/,
      /(\b(OR|AND)\b.*=.*)/i,
      /(;|\||&)/,
    ];

    return sqlPatterns.some((pattern) => pattern.test(input));
  }

  /**
   * Check for XSS patterns
   */
  static hasXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
    ];

    return xssPatterns.some((pattern) => pattern.test(input));
  }

  /**
   * Validate and sanitize input
   */
  static validateInput(
    input: any,
    type: "string" | "email" | "uuid" | "number"
  ): {
    isValid: boolean;
    sanitized?: any;
    errors: string[];
  } {
    const errors: string[] = [];

    if (input === null || input === undefined) {
      errors.push("Input is required");
      return { isValid: false, errors };
    }

    switch (type) {
      case "string": {
        if (typeof input !== "string") {
          errors.push("Input must be a string");
          return { isValid: false, errors };
        }

        if (InputValidator.hasSQLInjection(input)) {
          errors.push("Input contains potential SQL injection");
        }

        if (InputValidator.hasXSS(input)) {
          errors.push("Input contains potential XSS");
        }

        const sanitized = InputValidator.sanitizeString(input);
        return {
          isValid: errors.length === 0,
          sanitized,
          errors,
        };
      }

      case "email":
        if (typeof input !== "string" || !InputValidator.isValidEmail(input)) {
          errors.push("Invalid email format");
        }
        return {
          isValid: errors.length === 0,
          sanitized: input.toLowerCase().trim(),
          errors,
        };

      case "uuid":
        if (typeof input !== "string" || !InputValidator.isValidUUID(input)) {
          errors.push("Invalid UUID format");
        }
        return {
          isValid: errors.length === 0,
          sanitized: input,
          errors,
        };

      case "number": {
        const num = Number(input);
        if (isNaN(num)) {
          errors.push("Input must be a valid number");
        }
        return {
          isValid: errors.length === 0,
          sanitized: num,
          errors,
        };
      }

      default:
        errors.push("Unknown validation type");
        return { isValid: false, errors };
    }
  }
}

/**
 * Security headers manager
 */
export class SecurityHeaders {
  private config: SecurityConfig;

  constructor(config: SecurityConfig) {
    this.config = config;
  }

  /**
   * Get security headers for responses
   */
  getHeaders(): Record<string, string> {
    if (!this.config.enableSecurityHeaders) {
      return {};
    }

    return {
      // Prevent MIME type sniffing
      "X-Content-Type-Options": "nosniff",

      // Prevent clickjacking
      "X-Frame-Options": "DENY",

      // Enable XSS protection
      "X-XSS-Protection": "1; mode=block",

      // Control referrer information
      "Referrer-Policy": "strict-origin-when-cross-origin",

      // Content Security Policy
      "Content-Security-Policy": this.getCSPHeader(),

      // Permissions Policy
      "Permissions-Policy": "camera=(), microphone=(), geolocation=()",

      // Strict Transport Security (HTTPS only)
      "Strict-Transport-Security": "max-age=31536000; includeSubDomains",

      // Prevent caching of sensitive content
      "Cache-Control": "no-cache, no-store, must-revalidate",
      Pragma: "no-cache",
      Expires: "0",
    };
  }

  /**
   * Generate Content Security Policy header
   */
  private getCSPHeader(): string {
    const vercelEnv = getVercelEnvironment();

    const policies = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://vercel.live",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://api.openai.com https://api.deepseek.com https://api.stripe.com",
      "frame-src 'self' https://js.stripe.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
    ];

    // Add development-specific policies
    if (vercelEnv.isDevelopment) {
      policies.push("connect-src 'self' ws: wss: http://localhost:* https://localhost:*");
    }

    return policies.join("; ");
  }
}

/**
 * Main security middleware
 */
export class SecurityMiddleware {
  private rateLimiter: RateLimiter;
  private csrfProtection: CSRFProtection;
  private securityHeaders: SecurityHeaders;
  private config: SecurityConfig;

  constructor(config?: Partial<SecurityConfig>) {
    const vercelEnv = getVercelEnvironment();

    this.config = {
      enableRateLimit: vercelEnv.isProduction,
      enableCSRF: vercelEnv.isProduction,
      enableSecurityHeaders: vercelEnv.isProduction,
      enableInputValidation: true,
      rateLimitRequests: 100,
      rateLimitWindow: 60000, // 1 minute
      trustedOrigins: ["http://localhost:3000"],
      ...config,
    };

    this.rateLimiter = new RateLimiter(this.config);
    this.csrfProtection = new CSRFProtection(this.config);
    this.securityHeaders = new SecurityHeaders(this.config);
  }

  /**
   * Apply security middleware to a request
   */
  async applyMiddleware(request: Request): Promise<{
    allowed: boolean;
    response?: Response;
    headers?: Record<string, string>;
  }> {
    const clientIP = this.getClientIP(request);
    const origin = request.headers.get("origin");

    // Check rate limiting
    if (this.rateLimiter.isRateLimited(clientIP)) {
      const rateLimitInfo = this.rateLimiter.getRateLimitInfo(clientIP);

      return {
        allowed: false,
        response: new Response("Rate limit exceeded", {
          status: 429,
          headers: {
            "X-RateLimit-Limit": rateLimitInfo.limit.toString(),
            "X-RateLimit-Remaining": rateLimitInfo.remaining.toString(),
            "X-RateLimit-Reset": rateLimitInfo.reset.toString(),
            "Retry-After": Math.ceil((rateLimitInfo.reset - Date.now()) / 1000).toString(),
          },
        }),
      };
    }

    // Check CORS
    if (origin && !this.isOriginAllowed(origin)) {
      return {
        allowed: false,
        response: new Response("Origin not allowed", { status: 403 }),
      };
    }

    // Get security headers
    const headers = this.securityHeaders.getHeaders();

    // Add rate limit headers
    const rateLimitInfo = this.rateLimiter.getRateLimitInfo(clientIP);
    headers["X-RateLimit-Limit"] = rateLimitInfo.limit.toString();
    headers["X-RateLimit-Remaining"] = rateLimitInfo.remaining.toString();
    headers["X-RateLimit-Reset"] = rateLimitInfo.reset.toString();

    return {
      allowed: true,
      headers,
    };
  }

  /**
   * Validate CSRF token
   */
  validateCSRF(sessionId: string, token: string): boolean {
    return this.csrfProtection.validateToken(sessionId, token);
  }

  /**
   * Generate CSRF token
   */
  generateCSRFToken(sessionId: string): string {
    return this.csrfProtection.generateToken(sessionId);
  }

  /**
   * Get client IP address
   */
  private getClientIP(request: Request): string {
    return (
      request.headers.get("x-forwarded-for")?.split(",")[0] ||
      request.headers.get("x-real-ip") ||
      "unknown"
    );
  }

  /**
   * Check if origin is allowed
   */
  private isOriginAllowed(origin: string): boolean {
    return (
      this.config.trustedOrigins.includes(origin) ||
      origin.includes("vercel.app") ||
      origin.includes("localhost")
    );
  }
}

/**
 * Security middleware wrapper for API routes
 */
export function withSecurity<T extends (...args: any[]) => Promise<Response>>(
  handler: T,
  options?: Partial<SecurityConfig>
): T {
  const middleware = new SecurityMiddleware(options);

  return (async (...args: Parameters<T>) => {
    const request = args[0] as Request;

    const securityCheck = await middleware.applyMiddleware(request);

    if (!securityCheck.allowed) {
      return securityCheck.response!;
    }

    try {
      const response = await handler(...args);

      // Add security headers to response
      if (securityCheck.headers) {
        Object.entries(securityCheck.headers).forEach(([key, value]) => {
          response.headers.set(key, value);
        });
      }

      return response;
    } catch (error) {
      console.error("Handler error:", error);
      return new Response("Internal Server Error", { status: 500 });
    }
  }) as T;
}

/**
 * Default security middleware instance
 */
export const securityMiddleware = new SecurityMiddleware();
