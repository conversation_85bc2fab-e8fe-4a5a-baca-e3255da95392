/**
 * Session storage for Remix Auth
 * Handles secure session storage and retrieval
 */

import { createCookieSessionStorage } from "@remix-run/node";

// Use AUTH_COOKIE_KEY as session secret (already configured)
const sessionSecret = process.env.AUTH_COOKIE_KEY || process.env.SESSION_SECRET;
if (!sessionSecret) {
  throw new Error("AUTH_COOKIE_KEY or SESSION_SECRET must be set");
}

export const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: "__session",
    httpOnly: true,
    maxAge: 60 * 60 * 12, // 12 hours (as per blueprint)
    path: "/",
    sameSite: "lax",
    secrets: [sessionSecret],
    secure: process.env.NODE_ENV === "production",
  },
});

export const { getSession, commitSession, destroySession } = sessionStorage;
