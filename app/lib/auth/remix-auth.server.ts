/**
 * Remix Auth Configuration
 * Handles Google OAuth and Email/Password authentication
 */

import bcrypt from "bcryptjs";
import { eq } from "drizzle-orm";
import { Authenticator } from "remix-auth";
import { FormStrategy } from "remix-auth-form";
import { GoogleStrategy } from "remix-auth-google";
import { createDbFromEnv } from "../db/db";
import { users } from "../db/schemas/users";
import { sessionStorage } from "./session-storage.server";

// User type for authentication
export interface AuthUser {
  id: string;
  email: string;
  name: string | null;
  avatar: string | null;
  googleSub: string | null;
  emailVerified: boolean;
}

// Create an instance of the authenticator
export const authenticator = new Authenticator<AuthUser>(sessionStorage);

// Environment variables
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;

// Construct BASE_URL more reliably
let BASE_URL = "http://localhost:5173"; // Default for development

if (process.env.NODE_ENV === "production") {
  // Try different environment variables that Vercel provides
  if (process.env.VERCEL_URL) {
    BASE_URL = `https://${process.env.VERCEL_URL}`;
  } else if (process.env.VERCEL_PROJECT_PRODUCTION_URL) {
    BASE_URL = `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`;
  } else {
    // Fallback to a known production URL
    BASE_URL = "https://remix-vercel-neon-starter.vercel.app";
  }
} else if (process.env.APP_URL) {
  // Use APP_URL if provided (for custom domains)
  BASE_URL = process.env.APP_URL;
}

console.log("Auth BASE_URL:", BASE_URL);

if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) {
  console.warn("Google OAuth credentials not found. Google authentication will be disabled.");
  console.warn("GOOGLE_CLIENT_ID:", GOOGLE_CLIENT_ID ? "Set" : "Missing");
  console.warn("GOOGLE_CLIENT_SECRET:", GOOGLE_CLIENT_SECRET ? "Set" : "Missing");
}

// Google OAuth Strategy
if (GOOGLE_CLIENT_ID && GOOGLE_CLIENT_SECRET) {
  const callbackURL = `${BASE_URL}/auth/google/callback`;
  console.log("Google OAuth callback URL:", callbackURL);

  authenticator.use(
    new GoogleStrategy(
      {
        clientID: GOOGLE_CLIENT_ID,
        clientSecret: GOOGLE_CLIENT_SECRET,
        callbackURL,
      },
      async ({ accessToken, refreshToken, extraParams, profile }) => {
        // Find or create user from Google profile
        const googleSub = profile.id;
        const email = profile.emails[0].value;
        const name = profile.displayName;
        const avatar = profile.photos[0]?.value;

        try {
          const db = createDbFromEnv();

          // First, try to find user by Google sub
          let user = await db.query.users.findFirst({
            where: eq(users.googleSub, googleSub),
            columns: {
              id: true,
              email: true,
              name: true,
              avatar: true,
              googleSub: true,
              emailVerified: true,
            },
          });

          if (user) {
            // Update user info if needed
            if (user.email !== email || user.name !== name || user.avatar !== avatar) {
              await db
                .update(users)
                .set({
                  email,
                  name,
                  avatar,
                  emailVerified: true,
                  lastLoginAt: new Date(),
                  updatedAt: new Date(),
                })
                .where(eq(users.id, user.id));

              user = { ...user, email, name, avatar, emailVerified: true };
            } else {
              // Just update last login
              await db
                .update(users)
                .set({
                  lastLoginAt: new Date(),
                  updatedAt: new Date(),
                })
                .where(eq(users.id, user.id));
            }

            return user;
          }

          // If no user found by Google sub, check by email
          user = await db.query.users.findFirst({
            where: eq(users.email, email),
            columns: {
              id: true,
              email: true,
              name: true,
              avatar: true,
              googleSub: true,
              emailVerified: true,
            },
          });

          if (user) {
            // Link Google account to existing user
            await db
              .update(users)
              .set({
                googleSub,
                name: name || user.name,
                avatar: avatar || user.avatar,
                emailVerified: true,
                signinType: "oauth",
                signinProvider: "google",
                signinOpenid: googleSub,
                lastLoginAt: new Date(),
                updatedAt: new Date(),
              })
              .where(eq(users.id, user.id));

            return { ...user, googleSub, name: name || user.name, avatar: avatar || user.avatar };
          }

          // Create new user
          const [newUser] = await db
            .insert(users)
            .values({
              email,
              name,
              avatar,
              googleSub,
              emailVerified: true,
              signinType: "oauth",
              signinProvider: "google",
              signinOpenid: googleSub,
              lastLoginAt: new Date(),
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .returning({
              id: users.id,
              email: users.email,
              name: users.name,
              avatar: users.avatar,
              googleSub: users.googleSub,
              emailVerified: users.emailVerified,
            });

          return newUser;
        } catch (error) {
          console.error("Failed to authenticate with Google:", error);
          throw new Error("Failed to authenticate with Google");
        }
      }
    ),
    "google"
  );
}

// Email/Password Strategy
authenticator.use(
  new FormStrategy(async ({ form }) => {
    const email = form.get("email") as string;
    const password = form.get("password") as string;
    const action = form.get("action") as string;

    if (!email || !password) {
      throw new Error("Email and password are required");
    }

    const normalizedEmail = email.toLowerCase().trim();

    if (action === "signup") {
      // Sign up logic
      return await signUpUser(normalizedEmail, password, form.get("name") as string);
    } else {
      // Sign in logic
      return await signInUser(normalizedEmail, password);
    }
  }),
  "form"
);

// Helper function for user sign up
async function signUpUser(email: string, password: string, name?: string): Promise<AuthUser> {
  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new Error("Invalid email format");
  }

  // Validate password strength
  if (password.length < 8) {
    throw new Error("Password must be at least 8 characters long");
  }

  try {
    const db = createDbFromEnv();

    // Check if user already exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, email),
    });

    if (existingUser) {
      throw new Error("User with this email already exists");
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create user
    const [newUser] = await db
      .insert(users)
      .values({
        email,
        name: name || null,
        passwordHash,
        emailVerified: false,
        signinType: "email",
        signinProvider: "email",
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning({
        id: users.id,
        email: users.email,
        name: users.name,
        avatar: users.avatar,
        googleSub: users.googleSub,
        emailVerified: users.emailVerified,
      });

    return newUser;
  } catch (error) {
    console.error("Failed to create user:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Failed to create user account");
  }
}

// Helper function for user sign in
async function signInUser(email: string, password: string): Promise<AuthUser> {
  try {
    const db = createDbFromEnv();

    // Find user by email
    const user = await db.query.users.findFirst({
      where: eq(users.email, email),
      columns: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        googleSub: true,
        emailVerified: true,
        passwordHash: true,
      },
    });

    if (!user) {
      throw new Error("Invalid email or password");
    }

    if (!user.passwordHash) {
      throw new Error("This account was created with Google. Please sign in with Google.");
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.passwordHash);
    if (!isValidPassword) {
      throw new Error("Invalid email or password");
    }

    // Update last login
    await db
      .update(users)
      .set({
        lastLoginAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(users.id, user.id));

    // Return user without password hash
    const { passwordHash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    console.error("Authentication failed:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Authentication failed");
  }
}
