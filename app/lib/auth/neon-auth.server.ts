/**
 * Stack Auth (Neon Auth) Server-side Integration for Remix
 * Real implementation using @stackframe/stack
 */

import { StackServerApp } from "@stackframe/stack";
import type { AuthUser } from "./remix-auth.server";

// Stack Auth server configuration
export interface StackAuthServerConfig {
  projectId?: string;
  secretKey?: string;
  publishableKey?: string;
  enabled?: boolean;
}

// Get Stack Auth configuration from environment
export const getStackAuthServerConfig = (
  env?: Record<string, string | undefined>
): StackAuthServerConfig => {
  const envVars = env || process.env;
  return {
    projectId: envVars.VITE_STACK_PROJECT_ID,
    secretKey: envVars.STACK_SECRET_SERVER_KEY,
    publishableKey: envVars.VITE_STACK_PUBLISHABLE_CLIENT_KEY,
    enabled: !!(envVars.VITE_STACK_PROJECT_ID && envVars.STACK_SECRET_SERVER_KEY),
  };
};

// Initialize Stack Auth server app
let stackServerApp: StackServerApp | null = null;

export const getStackServerApp = (): StackServerApp | null => {
  if (stackServerApp) {
    return stackServerApp;
  }

  const config = getStackAuthServerConfig();
  
  if (!config.enabled || !config.projectId || !config.secretKey) {
    console.warn("Stack Auth: Configuration incomplete, Stack Auth disabled");
    return null;
  }

  try {
    stackServerApp = new StackServerApp({
      tokenStore: "nextjs-cookie", // Compatible with Remix
      projectId: config.projectId,
      projectSecret: config.secretKey,
      publishableClientKey: config.publishableKey,
    });
    
    console.log("Stack Auth: Server app initialized successfully");
    return stackServerApp;
  } catch (error) {
    console.error("Stack Auth: Failed to initialize server app:", error);
    return null;
  }
};

// Validate Stack Auth token and get user
export const validateStackAuthToken = async (
  token: string
): Promise<{ valid: boolean; user?: any; error?: string }> => {
  try {
    const stackApp = getStackServerApp();
    
    if (!stackApp) {
      return { valid: false, error: "Stack Auth not properly configured" };
    }

    // Validate the token with Stack Auth
    const user = await stackApp.getUser({ accessToken: token });
    
    if (!user) {
      return { valid: false, error: "Invalid Stack Auth token" };
    }

    return { valid: true, user };
  } catch (error) {
    console.error("Stack Auth: Token validation error:", error);
    return { valid: false, error: "Token validation failed" };
  }
};

// Get user from Stack Auth token via request
export const getStackAuthUser = async (
  request: Request
): Promise<{ success: boolean; user?: AuthUser; error?: string }> => {
  try {
    const stackApp = getStackServerApp();
    
    if (!stackApp) {
      return { success: false, error: "Stack Auth not enabled or configured" };
    }

    // Extract user from request using Stack Auth's built-in methods
    let user;
    try {
      user = await stackApp.getUser(request);
    } catch (error) {
      // If direct request parsing fails, try manual token extraction
      const authHeader = request.headers.get("Authorization");
      const cookieHeader = request.headers.get("Cookie");
      
      let token: string | null = null;
      
      if (authHeader?.startsWith("Bearer ")) {
        token = authHeader.substring(7);
      } else if (cookieHeader) {
        // Parse cookies to find Stack Auth token
        const cookies = cookieHeader.split(";").reduce(
          (acc, cookie) => {
            const [key, value] = cookie.trim().split("=");
            acc[key] = decodeURIComponent(value);
            return acc;
          },
          {} as Record<string, string>
        );
        
        // Stack Auth typically uses these cookie names
        token = cookies["stack-access-token"] || cookies["stack-refresh-token"];
      }
      
      if (token) {
        user = await stackApp.getUser({ accessToken: token });
      }
    }

    if (!user) {
      return { success: false, error: "No Stack Auth user found" };
    }

    // Convert Stack Auth user to our AuthUser format
    const authUser: AuthUser = {
      id: user.id,
      email: user.primaryEmail || "",
      name: user.displayName || user.selectedTeam?.displayName || null,
      avatar: user.profileImageUrl || null,
      googleSub: user.oauthProviders?.find(p => p.id === "google")?.providerUserId || null,
      emailVerified: user.primaryEmailVerified || false,
    };

    return { success: true, user: authUser };
  } catch (error) {
    console.error("Stack Auth: Get user error:", error);
    return { success: false, error: "Failed to get user from Stack Auth" };
  }
};

// Check if request is using Stack Auth
export const isStackAuthRequest = (request: Request): boolean => {
  const config = getStackAuthServerConfig();

  if (!config.enabled) {
    return false;
  }

  // Check for Stack Auth specific headers or cookies
  const authHeader = request.headers.get("Authorization");
  const cookieHeader = request.headers.get("Cookie");

  // Look for Stack Auth specific tokens
  if (authHeader?.startsWith("Bearer ")) {
    return true; // Could be Stack Auth token
  }

  if (cookieHeader?.includes("stack-access-token") || cookieHeader?.includes("stack-refresh-token")) {
    return true;
  }

  return false;
};

// Utility function to get Stack Auth signin URL
export const getStackAuthSigninUrl = (redirectUrl?: string): string => {
  const config = getStackAuthServerConfig();
  
  if (!config.projectId) {
    throw new Error("Stack Auth project ID not configured");
  }

  const baseUrl = `https://app.stack-auth.com/handler/${config.projectId}/signin`;
  
  if (redirectUrl) {
    return `${baseUrl}?redirect_url=${encodeURIComponent(redirectUrl)}`;
  }
  
  return baseUrl;
};

// Utility function to get Stack Auth signup URL  
export const getStackAuthSignupUrl = (redirectUrl?: string): string => {
  const config = getStackAuthServerConfig();
  
  if (!config.projectId) {
    throw new Error("Stack Auth project ID not configured");
  }

  const baseUrl = `https://app.stack-auth.com/handler/${config.projectId}/signup`;
  
  if (redirectUrl) {
    return `${baseUrl}?redirect_url=${encodeURIComponent(redirectUrl)}`;
  }
  
  return baseUrl;
};
