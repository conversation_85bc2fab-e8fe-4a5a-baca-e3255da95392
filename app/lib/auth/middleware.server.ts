/**
 * 统一认证中间件 - 支持 Remix Auth、Stack Auth 和 JWT
 */

import { redirect } from "@remix-run/node";
import { authenticator } from "./remix-auth.server";
import { getStackAuthUser, isStackAuthRequest } from "./neon-auth.server";
import { type AuthUser, extractJwtFromCookies, verifyJwt } from "./jwt.server";

// 认证结果接口
export interface AuthResult {
  success: boolean;
  user?: AuthUser & { jwt?: string; source?: 'remix' | 'stack' | 'jwt' };
  error?: string;
}

/**
 * 核心认证函数 - requireUser
 * 优先级：Stack Auth > Remix Auth > JWT
 */
export async function requireUser(request: Request): Promise<AuthUser & { jwt?: string; source?: 'remix' | 'stack' | 'jwt' }> {
  // 1. 首先尝试 Stack Auth
  if (isStackAuthRequest(request)) {
    const stackResult = await getStackAuthUser(request);
    if (stackResult.success && stackResult.user) {
      return { ...stackResult.user, source: 'stack' };
    }
  }

  // 2. 然后尝试 Remix Auth (session-based)
  try {
    const remixUser = await authenticator.isAuthenticated(request);
    if (remixUser) {
      return { ...remixUser, source: 'remix' };
    }
  } catch (error) {
    console.log("Remix auth check failed:", error);
  }

  // 3. 最后尝试 JWT
  const jwt = extractJwtFromCookies(request);
  if (jwt) {
    const jwtResult = await verifyJwt(jwt);
    if (jwtResult.success && jwtResult.user) {
      return { ...jwtResult.user, jwt, source: 'jwt' };
    }
  }

  // 如果所有认证方式都失败，重定向到登录
  throw redirect("/auth/login");
}

/**
 * 可选的非强制认证检查
 * 返回用户信息或 null，不会重定向
 */
export async function getUser(request: Request): Promise<AuthResult> {
  try {
    // 1. 首先尝试 Stack Auth
    if (isStackAuthRequest(request)) {
      const stackResult = await getStackAuthUser(request);
      if (stackResult.success && stackResult.user) {
        return { 
          success: true, 
          user: { ...stackResult.user, source: 'stack' } 
        };
      }
    }

    // 2. 然后尝试 Remix Auth
    try {
      const remixUser = await authenticator.isAuthenticated(request);
      if (remixUser) {
        return { 
          success: true, 
          user: { ...remixUser, source: 'remix' } 
        };
      }
    } catch (error) {
      console.log("Remix auth check failed:", error);
    }

    // 3. 最后尝试 JWT
    const jwt = extractJwtFromCookies(request);
    if (jwt) {
      const jwtResult = await verifyJwt(jwt);
      if (jwtResult.success && jwtResult.user) {
        return {
          success: true,
          user: { ...jwtResult.user, jwt, source: 'jwt' },
        };
      }
    }

    return { success: false, error: "No valid authentication found" };
  } catch (error) {
    console.error("Auth check error:", error);
    return { success: false, error: "Authentication failed" };
  }
}

/**
 * API专用认证函数 - 返回JSON错误而不是重定向
 * 用于API路由，不会重定向到登录页面
 */
export async function requireUserForAPI(request: Request): Promise<AuthUser & { jwt: string }> {
  // 从 Cookie 中提取 JWT
  const jwt = extractJwtFromCookies(request);

  if (!jwt) {
    throw new Response(
      JSON.stringify({
        success: false,
        error: "Authentication required",
        message: "Please log in to access this resource",
        code: 401,
      }),
      {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }

  // 验证 JWT
  const result = await verifyJwt(jwt);

  if (!result.success || !result.user) {
    throw new Response(
      JSON.stringify({
        success: false,
        error: "Invalid authentication",
        message: "Your session has expired. Please log in again",
        code: 401,
      }),
      {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }

  // 返回用户信息和 JWT（用于数据库查询）
  return { ...result.user, jwt };
}
